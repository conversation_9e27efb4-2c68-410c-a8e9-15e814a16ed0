[31m
13:09:57.159 [error] Exqlite.Connection (#PID<0.820.0>) failed to connect: ** (Exqlite.Error) database is locked
[0m
11:18:06.056 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

11:18:06.067 [debug] QUERY OK db=0.4ms decode=0.8ms idle=3.0ms
PRAGMA foreign_key_list(users) []

11:18:06.067 [debug] QUERY OK db=0.0ms idle=5.7ms
PRAGMA index_list(users) []

11:18:06.068 [debug] QUERY OK db=0.0ms idle=5.7ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:18:06.068 [debug] QUERY OK db=0.0ms idle=6.8ms
PRAGMA table_info(users) []

11:18:06.857 [debug] QUERY OK db=0.6ms decode=1.2ms queue=1.7ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:18:07.326 [debug] QUERY OK db=0.7ms decode=0.7ms queue=2.3ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:18:07.778 [debug] QUERY OK db=0.7ms decode=0.7ms queue=2.4ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:18:08.371 [debug] QUERY OK db=0.5ms decode=0.8ms idle=35.9ms
PRAGMA foreign_key_list(users) []

11:18:08.372 [debug] QUERY OK db=0.0ms idle=40.1ms
PRAGMA index_list(users) []

11:18:08.374 [debug] QUERY OK db=0.0ms idle=40.2ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:18:08.374 [debug] QUERY OK db=0.0ms idle=41.5ms
PRAGMA table_info(users) []

11:18:08.884 [debug] QUERY OK db=0.5ms decode=1.0ms queue=2.6ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:18:09.994 [debug] QUERY OK db=0.6ms decode=1.0ms idle=40.1ms
PRAGMA foreign_key_list(users) []

11:18:09.995 [debug] QUERY OK db=0.0ms idle=44.3ms
PRAGMA index_list(users) []

11:18:09.996 [debug] QUERY OK db=0.1ms idle=44.4ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:18:09.996 [debug] QUERY OK db=0.0ms idle=45.6ms
PRAGMA table_info(users) []

11:18:18.598 [info] Refreshing Drops.Relation cache...

11:18:18.605 [info] Repositories: [Sample.Repos.Sqlite]

11:18:18.605 [info] Tables: all

11:18:18.605 [info] Processing repository: Sample.Repos.Sqlite

11:18:18.614 [info]   Cache cleared for Sample.Repos.Sqlite

11:18:18.621 [debug] QUERY OK db=0.5ms decode=0.7ms idle=4.1ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:18:18.621 [info]   No tables found in Sample.Repos.Sqlite

11:18:18.621 [info]   Cache warmed up for 0 tables

11:18:18.621 [info] Successful: 1

11:18:18.621 [info] Failed: 0

11:18:19.294 [info] Refreshing Drops.Relation cache...

11:18:19.297 [info] Repositories: [Sample.Repos.Sqlite]

11:18:19.299 [info] Tables: ["users", "posts"]

11:18:19.299 [info] Processing repository: Sample.Repos.Sqlite

11:18:19.299 [info]   Cache cleared for Sample.Repos.Sqlite

11:18:19.300 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

11:18:19.312 [debug] QUERY OK db=0.6ms decode=1.0ms idle=4.4ms
PRAGMA foreign_key_list(users) []

11:18:19.312 [debug] QUERY OK db=0.1ms idle=6.8ms
PRAGMA index_list(users) []

11:18:19.312 [debug] QUERY OK db=0.0ms idle=5.9ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:18:19.312 [debug] QUERY OK db=0.0ms idle=5.9ms
PRAGMA table_info(users) []

11:18:19.326 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

11:18:19.326 [debug] QUERY OK db=0.0ms idle=19.5ms
PRAGMA foreign_key_list(posts) []

11:18:19.326 [debug] QUERY OK db=0.0ms idle=19.5ms
PRAGMA index_list(posts) []

11:18:19.326 [debug] QUERY OK db=0.0ms idle=19.5ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

11:18:19.326 [debug] QUERY OK db=0.0ms idle=19.6ms
PRAGMA table_info(posts) []

11:18:19.327 [info]   Cache warmed up for 2 tables

11:18:19.327 [info] Successful: 1

11:18:19.327 [info] Failed: 0

11:18:20.045 [debug] QUERY OK db=0.7ms decode=1.0ms idle=1.2ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:18:21.061 [info] Refreshing Drops.Relation cache...

11:18:21.064 [info] Repositories: [Sample.Repos.Sqlite]

11:18:21.064 [info] Tables: all

11:18:21.064 [info] Processing repository: Sample.Repos.Sqlite

11:18:21.064 [info]   Cache cleared for Sample.Repos.Sqlite

11:18:21.075 [debug] QUERY OK db=0.6ms decode=1.0ms idle=1.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:18:21.075 [info]   No tables found in Sample.Repos.Sqlite

11:18:21.075 [info]   Cache warmed up for 0 tables

11:18:21.075 [info] Successful: 1

11:18:21.075 [info] Failed: 0

11:18:21.507 [info] Refreshing Drops.Relation cache...

11:18:21.514 [info] Repositories: [Sample.Repos.Sqlite]

11:18:21.516 [info] Tables: ["non_existent_table"]

11:18:21.516 [info] Processing repository: Sample.Repos.Sqlite

11:18:21.524 [info]   Cache cleared for Sample.Repos.Sqlite

11:18:21.525 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.non_existent_table (not cached: {:file_read, :enoent})

11:18:21.534 [debug] QUERY OK db=0.7ms decode=1.1ms idle=12.1ms
PRAGMA foreign_key_list(non_existent_table) []

11:18:21.534 [debug] QUERY OK db=0.1ms queue=0.1ms idle=14.9ms
PRAGMA index_list(non_existent_table) []

11:18:21.534 [debug] QUERY OK db=0.0ms idle=15.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["non_existent_table"]

11:18:21.534 [debug] QUERY OK db=0.0ms idle=15.2ms
PRAGMA table_info(non_existent_table) []

11:18:21.546 [info]   Cache warmed up for 1 tables

11:18:21.548 [info] Successful: 1

11:18:21.548 [info] Failed: 0

11:18:21.971 [info] Refreshing Drops.Relation cache...

11:18:21.979 [info] Repositories: [Sample.Repos.Sqlite]

11:18:21.979 [info] Tables: all

11:18:21.979 [info] Processing repository: Sample.Repos.Sqlite

11:18:21.987 [info]   Cache cleared for Sample.Repos.Sqlite

11:18:21.993 [debug] QUERY OK db=0.4ms decode=0.7ms idle=3.3ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:18:21.993 [info]   No tables found in Sample.Repos.Sqlite

11:18:21.993 [info]   Cache warmed up for 0 tables

11:18:21.995 [info] Successful: 1

11:18:21.995 [info] Failed: 0

11:18:22.841 [info] Refreshing Drops.Relation cache...

11:18:22.849 [info] Repositories: [Sample.Repos.Sqlite]

11:18:22.849 [info] Tables: all

11:18:22.849 [info] Processing repository: Sample.Repos.Sqlite

11:18:22.857 [info]   Cache cleared for Sample.Repos.Sqlite

11:18:22.864 [debug] QUERY OK db=0.6ms decode=0.7ms idle=4.7ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:18:22.864 [info]   No tables found in Sample.Repos.Sqlite

11:18:22.864 [info]   Cache warmed up for 0 tables

11:18:22.867 [info] Successful: 1

11:18:22.867 [info] Failed: 0

11:18:23.291 [info] Refreshing Drops.Relation cache...

11:18:23.298 [info] Repositories: [Sample.Repos.Sqlite]

11:18:23.302 [info] Tables: ["''"]

11:18:23.302 [info] Processing repository: Sample.Repos.Sqlite

11:18:23.309 [info]   Cache cleared for Sample.Repos.Sqlite

11:18:23.310 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.'' (not cached: {:file_read, :enoent})

11:18:23.316 [debug] QUERY OK db=0.4ms decode=0.7ms idle=8.7ms
PRAGMA foreign_key_list('') []

11:18:23.316 [debug] QUERY OK db=0.0ms idle=10.3ms
PRAGMA index_list('') []

11:18:23.316 [debug] QUERY OK db=0.0ms idle=10.4ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["''"]

11:18:23.316 [debug] QUERY OK db=0.0ms idle=10.4ms
PRAGMA table_info('') []

11:18:23.327 [info]   Cache warmed up for 1 tables

11:18:23.329 [info] Successful: 1

11:18:23.329 [info] Failed: 0
[36m
11:20:26.882 [debug] QUERY OK db=0.0ms idle=2.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []
[0m[36m
11:20:26.883 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})
[0m[36m
11:20:26.884 [debug] QUERY OK db=0.0ms idle=4.1ms
PRAGMA foreign_key_list(comments) []
[0m[36m
11:20:26.884 [debug] QUERY OK db=0.0ms idle=4.5ms
PRAGMA index_list(comments) []
[0m[36m
11:20:26.884 [debug] QUERY OK db=0.0ms idle=4.6ms
PRAGMA index_info(comments_approved_index) []
[0m[36m
11:20:26.884 [debug] QUERY OK db=0.0ms idle=4.6ms
PRAGMA index_info(comments_post_id_index) []
[0m[36m
11:20:26.884 [debug] QUERY OK db=0.0ms idle=4.6ms
PRAGMA index_info(comments_user_id_index) []
[0m[36m
11:20:26.885 [debug] QUERY OK db=0.0ms idle=4.6ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]
[0m[36m
11:20:26.885 [debug] QUERY OK db=0.1ms idle=5.5ms
PRAGMA table_info(comments) []
[0m[36m
11:20:26.900 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})
[0m[36m
11:20:26.901 [debug] QUERY OK db=0.0ms idle=21.1ms
PRAGMA foreign_key_list(posts) []
[0m[36m
11:20:26.901 [debug] QUERY OK db=0.1ms idle=21.1ms
PRAGMA index_list(posts) []
[0m[36m
11:20:26.901 [debug] QUERY OK db=0.0ms idle=19.5ms
PRAGMA index_info(posts_title_index) []
[0m[36m
11:20:26.901 [debug] QUERY OK db=0.0ms idle=17.4ms
PRAGMA index_info(posts_published_index) []
[0m[36m
11:20:26.901 [debug] QUERY OK db=0.0ms idle=17.0ms
PRAGMA index_info(posts_user_id_index) []
[0m[36m
11:20:26.901 [debug] QUERY OK db=0.0ms idle=17.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
[0m[36m
11:20:26.901 [debug] QUERY OK db=0.0ms idle=17.0ms
PRAGMA table_info(posts) []
[0m[36m
11:20:26.902 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})
[0m[36m
11:20:26.902 [debug] QUERY OK db=0.0ms idle=17.8ms
PRAGMA foreign_key_list(users) []
[0m[36m
11:20:26.902 [debug] QUERY OK db=0.0ms idle=17.7ms
PRAGMA index_list(users) []
[0m[36m
11:20:26.902 [debug] QUERY OK db=0.0ms idle=16.8ms
PRAGMA index_info(users_active_index) []
[0m[36m
11:20:26.902 [debug] QUERY OK db=0.0ms idle=1.3ms
PRAGMA index_info(users_email_index) []
[0m[36m
11:20:26.902 [debug] QUERY OK db=0.0ms idle=1.2ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
[0m[36m
11:20:26.902 [debug] QUERY OK db=0.0ms idle=1.2ms
PRAGMA table_info(users) []
[0m[36m
11:21:58.297 [debug] QUERY OK db=0.0ms idle=8.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []
[0m[36m
11:21:58.298 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})
[0m[36m
11:21:58.298 [debug] QUERY OK db=0.0ms idle=9.4ms
PRAGMA foreign_key_list(comments) []
[0m[36m
11:21:58.298 [debug] QUERY OK db=0.0ms idle=9.5ms
PRAGMA index_list(comments) []
[0m[36m
11:21:58.298 [debug] QUERY OK db=0.0ms idle=9.1ms
PRAGMA index_info(comments_approved_index) []
[0m[36m
11:21:58.299 [debug] QUERY OK db=0.0ms idle=9.1ms
PRAGMA index_info(comments_post_id_index) []
[0m[36m
11:21:58.299 [debug] QUERY OK db=0.0ms idle=9.1ms
PRAGMA index_info(comments_user_id_index) []
[0m[36m
11:21:58.299 [debug] QUERY OK db=0.0ms idle=9.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]
[0m[36m
11:21:58.299 [debug] QUERY OK db=0.0ms idle=10.0ms
PRAGMA table_info(comments) []
[0m[36m
11:21:58.318 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})
[0m[36m
11:21:58.318 [debug] QUERY OK db=0.0ms idle=28.1ms
PRAGMA foreign_key_list(posts) []
[0m[36m
11:21:58.318 [debug] QUERY OK db=0.0ms idle=28.1ms
PRAGMA index_list(posts) []
[0m[36m
11:21:58.318 [debug] QUERY OK db=0.0ms idle=21.3ms
PRAGMA index_info(posts_title_index) []
[0m[36m
11:21:58.318 [debug] QUERY OK db=0.0ms idle=19.3ms
PRAGMA index_info(posts_published_index) []
[0m[36m
11:21:58.318 [debug] QUERY OK db=0.0ms idle=19.3ms
PRAGMA index_info(posts_user_id_index) []
[0m[36m
11:21:58.318 [debug] QUERY OK db=0.0ms idle=19.3ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
[0m[36m
11:21:58.318 [debug] QUERY OK db=0.0ms idle=19.3ms
PRAGMA table_info(posts) []
[0m[36m
11:21:58.318 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})
[0m[36m
11:21:58.318 [debug] QUERY OK db=0.0ms idle=19.8ms
PRAGMA foreign_key_list(users) []
[0m[36m
11:21:58.319 [debug] QUERY OK db=0.0ms idle=19.9ms
PRAGMA index_list(users) []
[0m[36m
11:21:58.319 [debug] QUERY OK db=0.0ms idle=19.0ms
PRAGMA index_info(users_active_index) []
[0m[36m
11:21:58.319 [debug] QUERY OK db=0.0ms idle=0.9ms
PRAGMA index_info(users_email_index) []
[0m[36m
11:21:58.319 [debug] QUERY OK db=0.0ms idle=0.9ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
[0m[36m
11:21:58.319 [debug] QUERY OK db=0.0ms idle=0.9ms
PRAGMA table_info(users) []
[0m
11:23:17.573 [info] Refreshing Drops.Relation cache...

11:23:17.577 [info] Repositories: [Sample.Repos.Sqlite]

11:23:17.577 [info] Tables: ["''"]

11:23:17.577 [info] Processing repository: Sample.Repos.Sqlite

11:23:17.590 [info]   Cache cleared for Sample.Repos.Sqlite

11:23:17.590 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.'' (not cached: {:file_read, :enoent})

11:23:17.596 [debug] QUERY OK db=0.6ms decode=0.9ms idle=3.0ms
PRAGMA foreign_key_list('') []

11:23:17.596 [debug] QUERY OK db=0.0ms idle=4.7ms
PRAGMA index_list('') []

11:23:17.596 [debug] QUERY OK db=0.0ms idle=4.8ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["''"]

11:23:17.596 [debug] QUERY OK db=0.0ms idle=4.9ms
PRAGMA table_info('') []

11:23:17.606 [info]   Cache warmed up for 1 tables

11:23:17.606 [info] Successful: 1

11:23:17.606 [info] Failed: 0

11:23:19.108 [info] Refreshing Drops.Relation cache...

11:23:19.115 [info] Repositories: [Sample.Repos.Sqlite]

11:23:19.115 [info] Tables: all

11:23:19.115 [info] Processing repository: Sample.Repos.Sqlite

11:23:19.125 [info]   Cache cleared for Sample.Repos.Sqlite

11:23:19.133 [debug] QUERY OK db=0.6ms decode=1.2ms idle=4.6ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:23:19.135 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

11:23:19.135 [debug] QUERY OK db=0.1ms idle=8.6ms
PRAGMA foreign_key_list(comments) []

11:23:19.135 [debug] QUERY OK db=0.0ms idle=8.8ms
PRAGMA index_list(comments) []

11:23:19.135 [debug] QUERY OK db=0.1ms idle=8.8ms
PRAGMA index_info(comments_approved_index) []

11:23:19.135 [debug] QUERY OK db=0.1ms idle=8.9ms
PRAGMA index_info(comments_post_id_index) []

11:23:19.136 [debug] QUERY OK db=0.1ms idle=9.1ms
PRAGMA index_info(comments_user_id_index) []

11:23:19.137 [debug] QUERY OK db=0.0ms idle=9.3ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

11:23:19.137 [debug] QUERY OK db=0.0ms idle=10.4ms
PRAGMA table_info(comments) []

11:23:19.155 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

11:23:19.155 [debug] QUERY OK db=0.0ms idle=28.6ms
PRAGMA foreign_key_list(posts) []

11:23:19.155 [debug] QUERY OK db=0.0ms idle=28.7ms
PRAGMA index_list(posts) []

11:23:19.155 [debug] QUERY OK db=0.0ms idle=23.4ms
PRAGMA index_info(posts_title_index) []

11:23:19.155 [debug] QUERY OK db=0.0ms idle=20.1ms
PRAGMA index_info(posts_published_index) []

11:23:19.155 [debug] QUERY OK db=0.0ms idle=20.0ms
PRAGMA index_info(posts_user_id_index) []

11:23:19.155 [debug] QUERY OK db=0.0ms idle=19.9ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

11:23:19.155 [debug] QUERY OK db=0.0ms idle=19.8ms
PRAGMA table_info(posts) []

11:23:19.156 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

11:23:19.156 [debug] QUERY OK db=0.0ms idle=20.4ms
PRAGMA foreign_key_list(users) []

11:23:19.156 [debug] QUERY OK db=0.0ms idle=20.4ms
PRAGMA index_list(users) []

11:23:19.156 [debug] QUERY OK db=0.0ms idle=19.3ms
PRAGMA index_info(users_active_index) []

11:23:19.156 [debug] QUERY OK db=0.0ms idle=1.2ms
PRAGMA index_info(users_email_index) []

11:23:19.156 [debug] QUERY OK db=0.0ms idle=1.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:23:19.156 [debug] QUERY OK db=0.0ms idle=1.1ms
PRAGMA table_info(users) []

11:23:19.157 [info]   Cache warmed up for 3 tables

11:23:19.159 [info] Successful: 1

11:23:19.159 [info] Failed: 0

11:23:20.012 [info] Refreshing Drops.Relation cache...

11:23:20.019 [info] Repositories: [Sample.Repos.Sqlite]

11:23:20.022 [info] Tables: ["non_existent_table"]

11:23:20.022 [info] Processing repository: Sample.Repos.Sqlite

11:23:20.025 [info]   Cache cleared for Sample.Repos.Sqlite

11:23:20.028 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.non_existent_table (not cached: {:file_read, :enoent})

11:23:20.034 [debug] QUERY OK db=0.5ms decode=0.9ms idle=5.0ms
PRAGMA foreign_key_list(non_existent_table) []

11:23:20.034 [debug] QUERY OK db=0.1ms idle=7.2ms
PRAGMA index_list(non_existent_table) []

11:23:20.035 [debug] QUERY OK db=0.0ms idle=7.4ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["non_existent_table"]

11:23:20.035 [debug] QUERY OK db=0.0ms idle=7.5ms
PRAGMA table_info(non_existent_table) []

11:23:20.049 [info]   Cache warmed up for 1 tables

11:23:20.049 [info] Successful: 1

11:23:20.049 [info] Failed: 0

11:23:20.759 [info] Refreshing Drops.Relation cache...

11:23:20.763 [info] Repositories: [Sample.Repos.Sqlite]

11:23:20.763 [info] Tables: all

11:23:20.763 [info] Processing repository: Sample.Repos.Sqlite

11:23:20.764 [info]   Cache cleared for Sample.Repos.Sqlite

11:23:20.774 [debug] QUERY OK db=0.5ms decode=0.8ms idle=2.3ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:23:20.775 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

11:23:20.775 [debug] QUERY OK db=0.0ms idle=4.4ms
PRAGMA foreign_key_list(comments) []

11:23:20.775 [debug] QUERY OK db=0.0ms idle=4.4ms
PRAGMA index_list(comments) []

11:23:20.775 [debug] QUERY OK db=0.0ms idle=4.5ms
PRAGMA index_info(comments_approved_index) []

11:23:20.775 [debug] QUERY OK db=0.0ms idle=4.5ms
PRAGMA index_info(comments_post_id_index) []

11:23:20.775 [debug] QUERY OK db=0.0ms idle=4.6ms
PRAGMA index_info(comments_user_id_index) []

11:23:20.776 [debug] QUERY OK db=0.0ms idle=4.7ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

11:23:20.776 [debug] QUERY OK db=0.0ms idle=5.7ms
PRAGMA table_info(comments) []

11:23:20.792 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

11:23:20.792 [debug] QUERY OK db=0.0ms queue=0.1ms idle=21.8ms
PRAGMA foreign_key_list(posts) []

11:23:20.792 [debug] QUERY OK db=0.0ms idle=22.1ms
PRAGMA index_list(posts) []

11:23:20.792 [debug] QUERY OK db=0.0ms idle=19.3ms
PRAGMA index_info(posts_title_index) []

11:23:20.793 [debug] QUERY OK db=0.0ms idle=17.8ms
PRAGMA index_info(posts_published_index) []

11:23:20.793 [debug] QUERY OK db=0.0ms idle=17.8ms
PRAGMA index_info(posts_user_id_index) []

11:23:20.793 [debug] QUERY OK db=0.0ms idle=17.8ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

11:23:20.793 [debug] QUERY OK db=0.0ms idle=17.9ms
PRAGMA table_info(posts) []

11:23:20.794 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

11:23:20.794 [debug] QUERY OK db=0.0ms idle=18.9ms
PRAGMA foreign_key_list(users) []

11:23:20.794 [debug] QUERY OK db=0.0ms idle=18.9ms
PRAGMA index_list(users) []

11:23:20.794 [debug] QUERY OK db=0.0ms idle=17.9ms
PRAGMA index_info(users_active_index) []

11:23:20.794 [debug] QUERY OK db=0.0ms idle=1.8ms
PRAGMA index_info(users_email_index) []

11:23:20.794 [debug] QUERY OK db=0.0ms idle=1.7ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:23:20.794 [debug] QUERY OK db=0.0ms idle=1.7ms
PRAGMA table_info(users) []

11:23:20.796 [info]   Cache warmed up for 3 tables

11:23:20.796 [info] Successful: 1

11:23:20.796 [info] Failed: 0

11:23:21.258 [debug] QUERY OK db=0.5ms decode=0.7ms idle=2.1ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:23:21.261 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

11:23:21.261 [debug] QUERY OK db=0.1ms idle=9.2ms
PRAGMA foreign_key_list(comments) []

11:23:21.261 [debug] QUERY OK db=0.0ms idle=9.5ms
PRAGMA index_list(comments) []

11:23:21.262 [debug] QUERY OK db=0.1ms idle=9.5ms
PRAGMA index_info(comments_approved_index) []

11:23:21.262 [debug] QUERY OK db=0.0ms idle=9.8ms
PRAGMA index_info(comments_post_id_index) []

11:23:21.262 [debug] QUERY OK db=0.0ms idle=9.8ms
PRAGMA index_info(comments_user_id_index) []

11:23:21.263 [debug] QUERY OK db=0.0ms idle=9.8ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

11:23:21.263 [debug] QUERY OK db=0.0ms idle=10.8ms
PRAGMA table_info(comments) []

11:23:21.278 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

11:23:21.278 [debug] QUERY OK db=0.0ms idle=26.4ms
PRAGMA foreign_key_list(posts) []

11:23:21.279 [debug] QUERY OK db=0.0ms idle=26.5ms
PRAGMA index_list(posts) []

11:23:21.279 [debug] QUERY OK db=0.0ms idle=24.0ms
PRAGMA index_info(posts_title_index) []

11:23:21.279 [debug] QUERY OK db=0.0ms idle=17.2ms
PRAGMA index_info(posts_published_index) []

11:23:21.279 [debug] QUERY OK db=0.0ms idle=17.1ms
PRAGMA index_info(posts_user_id_index) []

11:23:21.279 [debug] QUERY OK db=0.0ms idle=17.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

11:23:21.279 [debug] QUERY OK db=0.0ms idle=17.0ms
PRAGMA table_info(posts) []

11:23:21.280 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

11:23:21.280 [debug] QUERY OK db=0.0ms idle=18.2ms
PRAGMA foreign_key_list(users) []

11:23:21.280 [debug] QUERY OK db=0.0ms idle=18.3ms
PRAGMA index_list(users) []

11:23:21.280 [debug] QUERY OK db=0.0ms idle=17.3ms
PRAGMA index_info(users_active_index) []

11:23:21.280 [debug] QUERY OK db=0.0ms idle=1.8ms
PRAGMA index_info(users_email_index) []

11:23:21.280 [debug] QUERY OK db=0.0ms idle=1.8ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:23:21.280 [debug] QUERY OK db=0.0ms idle=1.8ms
PRAGMA table_info(users) []

11:23:22.144 [info] Refreshing Drops.Relation cache...

11:23:22.148 [info] Repositories: [Sample.Repos.Sqlite]

11:23:22.148 [info] Tables: all

11:23:22.149 [info] Processing repository: Sample.Repos.Sqlite

11:23:22.150 [info]   Cache cleared for Sample.Repos.Sqlite

11:23:22.158 [debug] QUERY OK db=0.4ms decode=0.7ms idle=3.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:23:22.159 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

11:23:22.159 [debug] QUERY OK db=0.0ms idle=5.1ms
PRAGMA foreign_key_list(comments) []

11:23:22.159 [debug] QUERY OK db=0.0ms idle=5.2ms
PRAGMA index_list(comments) []

11:23:22.159 [debug] QUERY OK db=0.0ms idle=5.2ms
PRAGMA index_info(comments_approved_index) []

11:23:22.159 [debug] QUERY OK db=0.0ms idle=5.3ms
PRAGMA index_info(comments_post_id_index) []

11:23:22.160 [debug] QUERY OK db=0.0ms idle=5.3ms
PRAGMA index_info(comments_user_id_index) []

11:23:22.160 [debug] QUERY OK db=0.0ms idle=5.5ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

11:23:22.161 [debug] QUERY OK db=0.0ms idle=6.4ms
PRAGMA table_info(comments) []

11:23:22.176 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

11:23:22.176 [debug] QUERY OK db=0.0ms idle=22.1ms
PRAGMA foreign_key_list(posts) []

11:23:22.176 [debug] QUERY OK db=0.0ms idle=22.2ms
PRAGMA index_list(posts) []

11:23:22.176 [debug] QUERY OK db=0.0ms idle=19.6ms
PRAGMA index_info(posts_title_index) []

11:23:22.176 [debug] QUERY OK db=0.0ms idle=17.1ms
PRAGMA index_info(posts_published_index) []

11:23:22.177 [debug] QUERY OK db=0.0ms idle=17.1ms
PRAGMA index_info(posts_user_id_index) []

11:23:22.177 [debug] QUERY OK db=0.0ms idle=17.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

11:23:22.177 [debug] QUERY OK db=0.0ms idle=17.1ms
PRAGMA table_info(posts) []

11:23:22.177 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

11:23:22.177 [debug] QUERY OK db=0.0ms idle=17.8ms
PRAGMA foreign_key_list(users) []

11:23:22.177 [debug] QUERY OK db=0.0ms idle=17.7ms
PRAGMA index_list(users) []

11:23:22.178 [debug] QUERY OK db=0.0ms idle=16.9ms
PRAGMA index_info(users_active_index) []

11:23:22.178 [debug] QUERY OK db=0.0ms idle=1.2ms
PRAGMA index_info(users_email_index) []

11:23:22.178 [debug] QUERY OK db=0.0ms idle=1.2ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:23:22.178 [debug] QUERY OK db=0.0ms idle=1.2ms
PRAGMA table_info(users) []

11:23:22.179 [info]   Cache warmed up for 3 tables

11:23:22.179 [info] Successful: 1

11:23:22.179 [info] Failed: 0

11:23:22.670 [info] Refreshing Drops.Relation cache...

11:23:22.676 [info] Repositories: [Sample.Repos.Sqlite]

11:23:22.676 [info] Tables: all

11:23:22.676 [info] Processing repository: Sample.Repos.Sqlite

11:23:22.685 [info]   Cache cleared for Sample.Repos.Sqlite

11:23:22.690 [debug] QUERY OK db=0.5ms decode=0.7ms idle=2.8ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:23:22.691 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

11:23:22.691 [debug] QUERY OK db=0.0ms idle=5.6ms
PRAGMA foreign_key_list(comments) []

11:23:22.691 [debug] QUERY OK db=0.0ms idle=5.7ms
PRAGMA index_list(comments) []

11:23:22.691 [debug] QUERY OK db=0.0ms idle=5.7ms
PRAGMA index_info(comments_approved_index) []

11:23:22.691 [debug] QUERY OK db=0.0ms idle=5.7ms
PRAGMA index_info(comments_post_id_index) []

11:23:22.691 [debug] QUERY OK db=0.0ms idle=5.7ms
PRAGMA index_info(comments_user_id_index) []

11:23:22.692 [debug] QUERY OK db=0.0ms idle=5.8ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

11:23:22.692 [debug] QUERY OK db=0.0ms idle=6.7ms
PRAGMA table_info(comments) []

11:23:22.707 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

11:23:22.707 [debug] QUERY OK db=0.0ms idle=22.1ms
PRAGMA foreign_key_list(posts) []

11:23:22.708 [debug] QUERY OK db=0.0ms idle=22.2ms
PRAGMA index_list(posts) []

11:23:22.708 [debug] QUERY OK db=0.0ms idle=18.9ms
PRAGMA index_info(posts_title_index) []

11:23:22.708 [debug] QUERY OK db=0.0ms idle=16.6ms
PRAGMA index_info(posts_published_index) []

11:23:22.708 [debug] QUERY OK db=0.0ms idle=16.6ms
PRAGMA index_info(posts_user_id_index) []

11:23:22.708 [debug] QUERY OK db=0.0ms idle=16.6ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

11:23:22.708 [debug] QUERY OK db=0.0ms idle=16.7ms
PRAGMA table_info(posts) []

11:23:22.708 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

11:23:22.708 [debug] QUERY OK db=0.0ms idle=17.3ms
PRAGMA foreign_key_list(users) []

11:23:22.709 [debug] QUERY OK db=0.0ms idle=17.3ms
PRAGMA index_list(users) []

11:23:22.709 [debug] QUERY OK db=0.0ms idle=16.5ms
PRAGMA index_info(users_active_index) []

11:23:22.709 [debug] QUERY OK db=0.0ms idle=1.1ms
PRAGMA index_info(users_email_index) []

11:23:22.709 [debug] QUERY OK db=0.0ms idle=1.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:23:22.709 [debug] QUERY OK db=0.0ms idle=1.0ms
PRAGMA table_info(users) []

11:23:22.710 [info]   Cache warmed up for 3 tables

11:23:22.711 [info] Successful: 1

11:23:22.711 [info] Failed: 0

11:23:23.152 [info] Refreshing Drops.Relation cache...

11:23:23.160 [info] Repositories: [Sample.Repos.Sqlite]

11:23:23.162 [info] Tables: ["users", "posts"]

11:23:23.162 [info] Processing repository: Sample.Repos.Sqlite

11:23:23.171 [info]   Cache cleared for Sample.Repos.Sqlite

11:23:23.172 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

11:23:23.177 [debug] QUERY OK db=0.4ms decode=0.6ms idle=9.1ms
PRAGMA foreign_key_list(users) []

11:23:23.177 [debug] QUERY OK db=0.0ms idle=10.7ms
PRAGMA index_list(users) []

11:23:23.177 [debug] QUERY OK db=0.0ms idle=10.8ms
PRAGMA index_info(users_active_index) []

11:23:23.177 [debug] QUERY OK db=0.0ms idle=10.9ms
PRAGMA index_info(users_email_index) []

11:23:23.177 [debug] QUERY OK db=0.0ms idle=11.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:23:23.177 [debug] QUERY OK db=0.0ms idle=11.0ms
PRAGMA table_info(users) []

11:23:23.194 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

11:23:23.194 [debug] QUERY OK db=0.0ms idle=28.2ms
PRAGMA foreign_key_list(posts) []

11:23:23.194 [debug] QUERY OK db=0.0ms idle=28.3ms
PRAGMA index_list(posts) []

11:23:23.194 [debug] QUERY OK db=0.0ms idle=28.3ms
PRAGMA index_info(posts_title_index) []

11:23:23.194 [debug] QUERY OK db=0.0ms idle=28.4ms
PRAGMA index_info(posts_published_index) []

11:23:23.194 [debug] QUERY OK db=0.0ms idle=18.8ms
PRAGMA index_info(posts_user_id_index) []

11:23:23.195 [debug] QUERY OK db=0.0ms idle=17.7ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

11:23:23.195 [debug] QUERY OK db=0.0ms idle=17.7ms
PRAGMA table_info(posts) []

11:23:23.200 [info]   Cache warmed up for 2 tables

11:23:23.202 [info] Successful: 1

11:23:23.202 [info] Failed: 0

11:23:52.724 [debug] QUERY OK db=0.6ms decode=1.0ms idle=45.0ms
PRAGMA foreign_key_list(users) []

11:23:52.725 [debug] QUERY OK db=0.0ms idle=49.1ms
PRAGMA index_list(users) []

11:23:52.726 [debug] QUERY OK db=0.0ms idle=49.2ms
PRAGMA index_info(users_active_index) []

11:23:52.726 [debug] QUERY OK db=0.0ms idle=49.4ms
PRAGMA index_info(users_email_index) []

11:23:52.727 [debug] QUERY OK db=0.1ms idle=49.5ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:23:52.727 [debug] QUERY OK db=0.0ms idle=50.7ms
PRAGMA table_info(users) []

11:23:53.254 [debug] QUERY OK db=0.4ms decode=0.7ms queue=2.3ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:23:53.262 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

11:23:53.262 [debug] QUERY OK db=0.0ms idle=10.3ms
PRAGMA foreign_key_list(comments) []

11:23:53.262 [debug] QUERY OK db=0.0ms idle=10.3ms
PRAGMA index_list(comments) []

11:23:53.262 [debug] QUERY OK db=0.0ms idle=10.4ms
PRAGMA index_info(comments_approved_index) []

11:23:53.262 [debug] QUERY OK db=0.0ms idle=10.5ms
PRAGMA index_info(comments_post_id_index) []

11:23:53.262 [debug] QUERY OK db=0.0ms idle=10.6ms
PRAGMA index_info(comments_user_id_index) []

11:23:53.263 [debug] QUERY OK db=0.0ms idle=10.6ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

11:23:53.263 [debug] QUERY OK db=0.0ms idle=11.4ms
PRAGMA table_info(comments) []

11:23:54.459 [debug] QUERY OK db=0.7ms decode=0.8ms queue=3.3ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:23:55.691 [debug] QUERY OK db=0.8ms decode=0.8ms queue=3.2ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:23:56.493 [debug] QUERY OK db=0.9ms decode=0.9ms idle=44.9ms
PRAGMA foreign_key_list(users) []

11:23:56.494 [debug] QUERY OK db=0.1ms idle=49.3ms
PRAGMA index_list(users) []

11:23:56.494 [debug] QUERY OK db=0.0ms idle=49.4ms
PRAGMA index_info(users_active_index) []

11:23:56.494 [debug] QUERY OK db=0.1ms idle=47.2ms
PRAGMA index_info(users_email_index) []

11:23:56.496 [debug] QUERY OK db=0.0ms idle=47.3ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:23:56.496 [debug] QUERY OK db=0.0ms idle=48.6ms
PRAGMA table_info(users) []

11:23:57.036 [debug] QUERY OK db=0.9ms decode=0.8ms queue=2.7ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []
[36m
11:31:21.890 [debug] QUERY OK db=0.0ms idle=7.4ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []
[0m[36m
11:31:21.891 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})
[0m[36m
11:31:21.892 [debug] QUERY OK db=0.0ms idle=8.9ms
PRAGMA foreign_key_list(comments) []
[0m[36m
11:31:21.892 [debug] QUERY OK db=0.0ms idle=9.0ms
PRAGMA index_list(comments) []
[0m[36m
11:31:21.892 [debug] QUERY OK db=0.0ms idle=9.0ms
PRAGMA index_info(comments_approved_index) []
[0m[36m
11:31:21.892 [debug] QUERY OK db=0.0ms idle=8.6ms
PRAGMA index_info(comments_post_id_index) []
[0m[36m
11:31:21.892 [debug] QUERY OK db=0.0ms idle=8.7ms
PRAGMA index_info(comments_user_id_index) []
[0m[36m
11:31:21.893 [debug] QUERY OK db=0.0ms idle=8.7ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]
[0m[36m
11:31:21.893 [debug] QUERY OK db=0.0ms idle=9.8ms
PRAGMA table_info(comments) []
[0m[36m
11:31:21.913 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})
[0m[36m
11:31:21.913 [debug] QUERY OK db=0.0ms idle=29.9ms
PRAGMA foreign_key_list(posts) []
[0m[36m
11:31:21.913 [debug] QUERY OK db=0.0ms idle=29.9ms
PRAGMA index_list(posts) []
[0m[36m
11:31:21.913 [debug] QUERY OK db=0.0ms idle=23.6ms
PRAGMA index_info(posts_title_index) []
[0m[36m
11:31:21.913 [debug] QUERY OK db=0.0ms idle=21.5ms
PRAGMA index_info(posts_published_index) []
[0m[36m
11:31:21.913 [debug] QUERY OK db=0.0ms idle=21.5ms
PRAGMA index_info(posts_user_id_index) []
[0m[36m
11:31:21.913 [debug] QUERY OK db=0.0ms idle=21.5ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
[0m[36m
11:31:21.913 [debug] QUERY OK db=0.0ms idle=21.6ms
PRAGMA table_info(posts) []
[0m[36m
11:31:21.914 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})
[0m[36m
11:31:21.914 [debug] QUERY OK db=0.0ms idle=22.2ms
PRAGMA foreign_key_list(users) []
[0m[36m
11:31:21.914 [debug] QUERY OK db=0.0ms idle=22.2ms
PRAGMA index_list(users) []
[0m[36m
11:31:21.914 [debug] QUERY OK db=0.0ms idle=21.0ms
PRAGMA index_info(users_active_index) []
[0m[36m
11:31:21.914 [debug] QUERY OK db=0.0ms idle=1.0ms
PRAGMA index_info(users_email_index) []
[0m[36m
11:31:21.914 [debug] QUERY OK db=0.0ms idle=1.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
[0m[36m
11:31:21.914 [debug] QUERY OK db=0.0ms idle=1.0ms
PRAGMA table_info(users) []
[0m[36m
11:31:22.417 [debug] QUERY OK db=0.6ms decode=0.7ms idle=1.9ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []
[0m[36m
11:31:22.420 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})
[0m[36m
11:31:22.420 [debug] QUERY OK db=0.1ms idle=9.3ms
PRAGMA foreign_key_list(comments) []
[0m[36m
11:31:22.420 [debug] QUERY OK db=0.0ms idle=9.5ms
PRAGMA index_list(comments) []
[0m[36m
11:31:22.420 [debug] QUERY OK db=0.0ms idle=9.6ms
PRAGMA index_info(comments_approved_index) []
[0m[36m
11:31:22.420 [debug] QUERY OK db=0.0ms idle=9.7ms
PRAGMA index_info(comments_post_id_index) []
[0m[36m
11:31:22.420 [debug] QUERY OK db=0.0ms idle=9.8ms
PRAGMA index_info(comments_user_id_index) []
[0m[36m
11:31:22.421 [debug] QUERY OK db=0.0ms idle=9.8ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]
[0m[36m
11:31:22.421 [debug] QUERY OK db=0.1ms idle=10.7ms
PRAGMA table_info(comments) []
[0m[36m
11:31:22.439 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})
[0m[36m
11:31:22.439 [debug] QUERY OK db=0.0ms idle=28.5ms
PRAGMA foreign_key_list(posts) []
[0m[36m
11:31:22.439 [debug] QUERY OK db=0.0ms idle=28.6ms
PRAGMA index_list(posts) []
[0m[36m
11:31:22.439 [debug] QUERY OK db=0.0ms idle=26.1ms
PRAGMA index_info(posts_title_index) []
[0m[36m
11:31:22.439 [debug] QUERY OK db=0.0ms idle=19.2ms
PRAGMA index_info(posts_published_index) []
[0m[36m
11:31:22.439 [debug] QUERY OK db=0.0ms idle=19.2ms
PRAGMA index_info(posts_user_id_index) []
[0m[36m
11:31:22.439 [debug] QUERY OK db=0.0ms idle=19.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]
[0m[36m
11:31:22.439 [debug] QUERY OK db=0.0ms idle=19.2ms
PRAGMA table_info(posts) []
[0m[36m
11:31:22.440 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})
[0m[36m
11:31:22.440 [debug] QUERY OK db=0.0ms idle=19.8ms
PRAGMA foreign_key_list(users) []
[0m[36m
11:31:22.440 [debug] QUERY OK db=0.0ms idle=19.8ms
PRAGMA index_list(users) []
[0m[36m
11:31:22.440 [debug] QUERY OK db=0.0ms idle=18.9ms
PRAGMA index_info(users_active_index) []
[0m[36m
11:31:22.440 [debug] QUERY OK db=0.0ms idle=1.1ms
PRAGMA index_info(users_email_index) []
[0m[36m
11:31:22.440 [debug] QUERY OK db=0.0ms idle=1.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]
[0m[36m
11:31:22.440 [debug] QUERY OK db=0.0ms idle=1.1ms
PRAGMA table_info(users) []
[0m
11:55:11.432 [info] Refreshing Drops.Relation cache...

11:55:11.433 [info] Repositories: [Sample.Repos.Sqlite]

11:55:11.433 [info] Tables: all

11:55:11.433 [info] Processing repository: Sample.Repos.Sqlite

11:55:11.435 [info]   Cache cleared for Sample.Repos.Sqlite

11:55:11.437 [debug] QUERY OK db=0.0ms queue=1.5ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:55:11.439 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

11:55:11.439 [debug] QUERY OK db=0.0ms idle=1.3ms
PRAGMA foreign_key_list(comments) []

11:55:11.439 [debug] QUERY OK db=0.0ms idle=1.3ms
PRAGMA index_list(comments) []

11:55:11.439 [debug] QUERY OK db=0.0ms idle=1.3ms
PRAGMA index_info(comments_approved_index) []

11:55:11.439 [debug] QUERY OK db=0.0ms idle=1.4ms
PRAGMA index_info(comments_post_id_index) []

11:55:11.439 [debug] QUERY OK db=0.0ms idle=1.4ms
PRAGMA index_info(comments_user_id_index) []

11:55:11.439 [debug] QUERY OK db=0.0ms idle=1.4ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

11:55:11.439 [debug] QUERY OK db=0.0ms idle=1.5ms
PRAGMA table_info(comments) []

11:55:11.443 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

11:55:11.443 [debug] QUERY OK db=0.0ms idle=4.6ms
PRAGMA foreign_key_list(posts) []

11:55:11.443 [debug] QUERY OK db=0.0ms idle=4.7ms
PRAGMA index_list(posts) []

11:55:11.443 [debug] QUERY OK db=0.0ms idle=4.8ms
PRAGMA index_info(posts_title_index) []

11:55:11.443 [debug] QUERY OK db=0.0ms idle=4.4ms
PRAGMA index_info(posts_published_index) []

11:55:11.443 [debug] QUERY OK db=0.0ms idle=4.4ms
PRAGMA index_info(posts_user_id_index) []

11:55:11.443 [debug] QUERY OK db=0.0ms idle=4.4ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

11:55:11.443 [debug] QUERY OK db=0.0ms idle=4.4ms
PRAGMA table_info(posts) []

11:55:11.444 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

11:55:11.444 [debug] QUERY OK db=0.0ms idle=5.4ms
PRAGMA foreign_key_list(users) []

11:55:11.444 [debug] QUERY OK db=0.0ms idle=5.5ms
PRAGMA index_list(users) []

11:55:11.445 [debug] QUERY OK db=0.0ms idle=5.5ms
PRAGMA index_info(users_active_index) []

11:55:11.445 [debug] QUERY OK db=0.0ms idle=1.6ms
PRAGMA index_info(users_email_index) []

11:55:11.445 [debug] QUERY OK db=0.0ms idle=1.6ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:55:11.445 [debug] QUERY OK db=0.0ms idle=1.6ms
PRAGMA table_info(users) []

11:55:11.446 [info]   Cache warmed up for 3 tables

11:55:11.446 [info] Successful: 1

11:55:11.446 [info] Failed: 0

11:55:12.082 [info] Refreshing Drops.Relation cache...

11:55:12.087 [info] Repositories: [Sample.Repos.Sqlite]

11:55:12.087 [info] Tables: all

11:55:12.088 [info] Processing repository: Sample.Repos.Sqlite

11:55:12.091 [info]   Cache cleared for Sample.Repos.Sqlite

11:55:12.098 [debug] QUERY OK db=0.9ms decode=0.8ms idle=4.4ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:55:12.099 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

11:55:12.100 [debug] QUERY OK db=0.2ms queue=0.1ms idle=8.4ms
PRAGMA foreign_key_list(comments) []

11:55:12.100 [debug] QUERY OK db=0.0ms idle=8.8ms
PRAGMA index_list(comments) []

11:55:12.100 [debug] QUERY OK db=0.0ms idle=8.8ms
PRAGMA index_info(comments_approved_index) []

11:55:12.100 [debug] QUERY OK db=0.0ms idle=8.9ms
PRAGMA index_info(comments_post_id_index) []

11:55:12.100 [debug] QUERY OK db=0.0ms idle=8.9ms
PRAGMA index_info(comments_user_id_index) []

11:55:12.101 [debug] QUERY OK db=0.0ms idle=9.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

11:55:12.101 [debug] QUERY OK db=0.0ms idle=10.0ms
PRAGMA table_info(comments) []

11:55:12.115 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

11:55:12.115 [debug] QUERY OK db=0.0ms idle=24.0ms
PRAGMA foreign_key_list(posts) []

11:55:12.115 [debug] QUERY OK db=0.0ms idle=24.1ms
PRAGMA index_list(posts) []

11:55:12.115 [debug] QUERY OK db=0.0ms idle=18.8ms
PRAGMA index_info(posts_title_index) []

11:55:12.115 [debug] QUERY OK db=0.0ms idle=15.6ms
PRAGMA index_info(posts_published_index) []

11:55:12.115 [debug] QUERY OK db=0.0ms idle=15.6ms
PRAGMA index_info(posts_user_id_index) []

11:55:12.116 [debug] QUERY OK db=0.0ms idle=15.6ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

11:55:12.116 [debug] QUERY OK db=0.0ms idle=15.6ms
PRAGMA table_info(posts) []

11:55:12.116 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

11:55:12.117 [debug] QUERY OK db=0.0ms idle=16.5ms
PRAGMA foreign_key_list(users) []

11:55:12.117 [debug] QUERY OK db=0.0ms idle=16.5ms
PRAGMA index_list(users) []

11:55:12.117 [debug] QUERY OK db=0.0ms idle=15.5ms
PRAGMA index_info(users_active_index) []

11:55:12.117 [debug] QUERY OK db=0.0ms idle=1.5ms
PRAGMA index_info(users_email_index) []

11:55:12.117 [debug] QUERY OK db=0.0ms idle=1.4ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:55:12.117 [debug] QUERY OK db=0.0ms idle=1.4ms
PRAGMA table_info(users) []

11:55:12.119 [info]   Cache warmed up for 3 tables

11:55:12.119 [info] Successful: 1

11:55:12.119 [info] Failed: 0

11:55:12.750 [info] Refreshing Drops.Relation cache...

11:55:12.754 [info] Repositories: [Sample.Repos.Sqlite]

11:55:12.754 [info] Tables: all

11:55:12.754 [info] Processing repository: Sample.Repos.Sqlite

11:55:12.755 [info]   Cache cleared for Sample.Repos.Sqlite

11:55:12.765 [debug] QUERY OK db=0.8ms decode=0.7ms idle=2.4ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:55:12.766 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

11:55:12.766 [debug] QUERY OK db=0.0ms idle=5.9ms
PRAGMA foreign_key_list(comments) []

11:55:12.767 [debug] QUERY OK db=0.0ms idle=6.0ms
PRAGMA index_list(comments) []

11:55:12.767 [debug] QUERY OK db=0.0ms idle=6.0ms
PRAGMA index_info(comments_approved_index) []

11:55:12.767 [debug] QUERY OK db=0.0ms idle=6.1ms
PRAGMA index_info(comments_post_id_index) []

11:55:12.767 [debug] QUERY OK db=0.0ms idle=6.2ms
PRAGMA index_info(comments_user_id_index) []

11:55:12.767 [debug] QUERY OK db=0.0ms idle=6.2ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

11:55:12.768 [debug] QUERY OK db=0.0ms idle=7.0ms
PRAGMA table_info(comments) []

11:55:12.782 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

11:55:12.782 [debug] QUERY OK db=0.0ms idle=21.4ms
PRAGMA foreign_key_list(posts) []

11:55:12.782 [debug] QUERY OK db=0.1ms idle=21.5ms
PRAGMA index_list(posts) []

11:55:12.782 [debug] QUERY OK db=0.0ms idle=18.4ms
PRAGMA index_info(posts_title_index) []

11:55:12.782 [debug] QUERY OK db=0.0ms idle=15.8ms
PRAGMA index_info(posts_published_index) []

11:55:12.782 [debug] QUERY OK db=0.0ms idle=15.7ms
PRAGMA index_info(posts_user_id_index) []

11:55:12.782 [debug] QUERY OK db=0.0ms idle=15.7ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

11:55:12.783 [debug] QUERY OK db=0.0ms idle=15.8ms
PRAGMA table_info(posts) []

11:55:12.783 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

11:55:12.784 [debug] QUERY OK db=0.0ms idle=16.7ms
PRAGMA foreign_key_list(users) []

11:55:12.784 [debug] QUERY OK db=0.0ms idle=16.7ms
PRAGMA index_list(users) []

11:55:12.784 [debug] QUERY OK db=0.0ms idle=16.0ms
PRAGMA index_info(users_active_index) []

11:55:12.784 [debug] QUERY OK db=0.0ms idle=1.6ms
PRAGMA index_info(users_email_index) []

11:55:12.784 [debug] QUERY OK db=0.0ms idle=1.5ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:55:12.784 [debug] QUERY OK db=0.0ms idle=1.5ms
PRAGMA table_info(users) []

11:55:12.786 [info]   Cache warmed up for 3 tables

11:55:12.786 [info] Successful: 1

11:55:12.786 [info] Failed: 0

11:55:13.222 [debug] QUERY OK db=0.4ms decode=0.4ms idle=1.9ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:55:13.224 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

11:55:13.224 [debug] QUERY OK db=0.0ms idle=6.7ms
PRAGMA foreign_key_list(comments) []

11:55:13.224 [debug] QUERY OK db=0.0ms idle=6.8ms
PRAGMA index_list(comments) []

11:55:13.224 [debug] QUERY OK db=0.0ms idle=6.9ms
PRAGMA index_info(comments_approved_index) []

11:55:13.224 [debug] QUERY OK db=0.0ms idle=7.0ms
PRAGMA index_info(comments_post_id_index) []

11:55:13.224 [debug] QUERY OK db=0.0ms idle=7.0ms
PRAGMA index_info(comments_user_id_index) []

11:55:13.224 [debug] QUERY OK db=0.0ms idle=7.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

11:55:13.225 [debug] QUERY OK db=0.0ms idle=7.6ms
PRAGMA table_info(comments) []

11:55:13.242 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

11:55:13.242 [debug] QUERY OK db=0.0ms idle=25.1ms
PRAGMA foreign_key_list(posts) []

11:55:13.242 [debug] QUERY OK db=0.0ms idle=25.2ms
PRAGMA index_list(posts) []

11:55:13.242 [debug] QUERY OK db=0.0ms idle=22.9ms
PRAGMA index_info(posts_title_index) []

11:55:13.242 [debug] QUERY OK db=0.0ms idle=18.6ms
PRAGMA index_info(posts_published_index) []

11:55:13.242 [debug] QUERY OK db=0.0ms idle=18.5ms
PRAGMA index_info(posts_user_id_index) []

11:55:13.242 [debug] QUERY OK db=0.0ms idle=18.5ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

11:55:13.243 [debug] QUERY OK db=0.0ms idle=18.6ms
PRAGMA table_info(posts) []

11:55:13.243 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

11:55:13.244 [debug] QUERY OK db=0.0ms idle=19.5ms
PRAGMA foreign_key_list(users) []

11:55:13.244 [debug] QUERY OK db=0.0ms idle=19.5ms
PRAGMA index_list(users) []

11:55:13.244 [debug] QUERY OK db=0.0ms idle=19.0ms
PRAGMA index_info(users_active_index) []

11:55:13.244 [debug] QUERY OK db=0.0ms idle=1.6ms
PRAGMA index_info(users_email_index) []

11:55:13.244 [debug] QUERY OK db=0.0ms idle=1.6ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:55:13.244 [debug] QUERY OK db=0.0ms idle=1.6ms
PRAGMA table_info(users) []

11:55:13.883 [info] Refreshing Drops.Relation cache...

11:55:13.886 [info] Repositories: [Sample.Repos.Sqlite]

11:55:13.886 [info] Tables: ["non_existent_table"]

11:55:13.886 [info] Processing repository: Sample.Repos.Sqlite

11:55:13.889 [info]   Cache cleared for Sample.Repos.Sqlite

11:55:13.890 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.non_existent_table (not cached: {:file_read, :enoent})

11:55:13.896 [debug] QUERY OK db=0.6ms decode=0.7ms idle=4.2ms
PRAGMA foreign_key_list(non_existent_table) []

11:55:13.897 [debug] QUERY OK db=0.0ms idle=6.4ms
PRAGMA index_list(non_existent_table) []

11:55:13.897 [debug] QUERY OK db=0.0ms idle=5.3ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["non_existent_table"]

11:55:13.897 [debug] QUERY OK db=0.0ms idle=5.4ms
PRAGMA table_info(non_existent_table) []

11:55:13.905 [info]   Cache warmed up for 1 tables

11:55:13.905 [info] Successful: 1

11:55:13.905 [info] Failed: 0

11:55:14.939 [info] Refreshing Drops.Relation cache...

11:55:14.945 [info] Repositories: [Sample.Repos.Sqlite]

11:55:14.945 [info] Tables: all

11:55:14.945 [info] Processing repository: Sample.Repos.Sqlite

11:55:14.951 [info]   Cache cleared for Sample.Repos.Sqlite

11:55:14.957 [debug] QUERY OK db=0.6ms decode=0.6ms idle=8.9ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:55:14.958 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

11:55:14.958 [debug] QUERY OK db=0.0ms idle=11.6ms
PRAGMA foreign_key_list(comments) []

11:55:14.958 [debug] QUERY OK db=0.0ms idle=11.7ms
PRAGMA index_list(comments) []

11:55:14.958 [debug] QUERY OK db=0.0ms idle=11.7ms
PRAGMA index_info(comments_approved_index) []

11:55:14.958 [debug] QUERY OK db=0.0ms idle=11.8ms
PRAGMA index_info(comments_post_id_index) []

11:55:14.958 [debug] QUERY OK db=0.0ms idle=11.9ms
PRAGMA index_info(comments_user_id_index) []

11:55:14.959 [debug] QUERY OK db=0.0ms idle=11.9ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

11:55:14.959 [debug] QUERY OK db=0.1ms idle=12.6ms
PRAGMA table_info(comments) []

11:55:14.977 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

11:55:14.978 [debug] QUERY OK db=0.1ms idle=31.1ms
PRAGMA foreign_key_list(posts) []

11:55:14.978 [debug] QUERY OK db=0.0ms idle=31.1ms
PRAGMA index_list(posts) []

11:55:14.978 [debug] QUERY OK db=0.0ms idle=21.9ms
PRAGMA index_info(posts_title_index) []

11:55:14.978 [debug] QUERY OK db=0.0ms idle=19.8ms
PRAGMA index_info(posts_published_index) []

11:55:14.978 [debug] QUERY OK db=0.0ms idle=19.8ms
PRAGMA index_info(posts_user_id_index) []

11:55:14.978 [debug] QUERY OK db=0.0ms idle=19.8ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

11:55:14.978 [debug] QUERY OK db=0.0ms idle=19.9ms
PRAGMA table_info(posts) []

11:55:14.980 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

11:55:14.980 [debug] QUERY OK db=0.0ms idle=21.3ms
PRAGMA foreign_key_list(users) []

11:55:14.980 [debug] QUERY OK db=0.0ms idle=21.3ms
PRAGMA index_list(users) []

11:55:14.980 [debug] QUERY OK db=0.0ms idle=20.5ms
PRAGMA index_info(users_active_index) []

11:55:14.980 [debug] QUERY OK db=0.0ms idle=2.2ms
PRAGMA index_info(users_email_index) []

11:55:14.980 [debug] QUERY OK db=0.0ms idle=2.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:55:14.980 [debug] QUERY OK db=0.0ms idle=2.1ms
PRAGMA table_info(users) []

11:55:14.982 [info]   Cache warmed up for 3 tables

11:55:14.983 [info] Successful: 1

11:55:14.983 [info] Failed: 0

11:55:15.978 [info] Refreshing Drops.Relation cache...

11:55:15.982 [info] Repositories: [Sample.Repos.Sqlite]

11:55:15.984 [info] Tables: ["''"]

11:55:15.984 [info] Processing repository: Sample.Repos.Sqlite

11:55:15.986 [info]   Cache cleared for Sample.Repos.Sqlite

11:55:15.988 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.'' (not cached: {:file_read, :enoent})

11:55:15.993 [debug] QUERY OK db=0.7ms decode=0.6ms idle=4.3ms
PRAGMA foreign_key_list('') []

11:55:15.993 [debug] QUERY OK db=0.0ms idle=6.3ms
PRAGMA index_list('') []

11:55:15.994 [debug] QUERY OK db=0.0ms idle=6.4ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["''"]

11:55:15.994 [debug] QUERY OK db=0.0ms idle=6.5ms
PRAGMA table_info('') []

11:55:16.004 [info]   Cache warmed up for 1 tables

11:55:16.004 [info] Successful: 1

11:55:16.004 [info] Failed: 0

11:55:16.646 [info] Refreshing Drops.Relation cache...

11:55:16.648 [info] Repositories: [Sample.Repos.Sqlite]

11:55:16.650 [info] Tables: ["users", "posts"]

11:55:16.650 [info] Processing repository: Sample.Repos.Sqlite

11:55:16.650 [info]   Cache cleared for Sample.Repos.Sqlite

11:55:16.650 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

11:55:16.657 [debug] QUERY OK db=0.4ms decode=0.4ms idle=1.9ms
PRAGMA foreign_key_list(users) []

11:55:16.657 [debug] QUERY OK db=0.1ms idle=3.3ms
PRAGMA index_list(users) []

11:55:16.657 [debug] QUERY OK db=0.0ms idle=3.5ms
PRAGMA index_info(users_active_index) []

11:55:16.657 [debug] QUERY OK db=0.0ms idle=3.6ms
PRAGMA index_info(users_email_index) []

11:55:16.657 [debug] QUERY OK db=0.0ms idle=3.6ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:55:16.657 [debug] QUERY OK db=0.0ms idle=3.7ms
PRAGMA table_info(users) []

11:55:16.670 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

11:55:16.671 [debug] QUERY OK db=0.0ms idle=17.0ms
PRAGMA foreign_key_list(posts) []

11:55:16.671 [debug] QUERY OK db=0.0ms idle=17.1ms
PRAGMA index_list(posts) []

11:55:16.671 [debug] QUERY OK db=0.0ms idle=17.1ms
PRAGMA index_info(posts_title_index) []

11:55:16.671 [debug] QUERY OK db=0.0ms idle=17.2ms
PRAGMA index_info(posts_published_index) []

11:55:16.671 [debug] QUERY OK db=0.0ms idle=14.9ms
PRAGMA index_info(posts_user_id_index) []

11:55:16.671 [debug] QUERY OK db=0.0ms idle=13.8ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

11:55:16.671 [debug] QUERY OK db=0.0ms idle=13.8ms
PRAGMA table_info(posts) []

11:55:16.673 [info]   Cache warmed up for 2 tables

11:55:16.673 [info] Successful: 1

11:55:16.673 [info] Failed: 0

11:55:31.701 [debug] QUERY OK db=0.5ms decode=0.7ms idle=30.5ms
PRAGMA foreign_key_list(users) []

11:55:31.702 [debug] QUERY OK db=0.0ms idle=34.7ms
PRAGMA index_list(users) []

11:55:31.702 [debug] QUERY OK db=0.0ms idle=34.9ms
PRAGMA index_info(users_active_index) []

11:55:31.702 [debug] QUERY OK db=0.0ms idle=34.9ms
PRAGMA index_info(users_email_index) []

11:55:31.703 [debug] QUERY OK db=0.0ms idle=35.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:55:31.703 [debug] QUERY OK db=0.1ms idle=35.8ms
PRAGMA table_info(users) []

11:55:32.223 [debug] QUERY OK db=1.0ms decode=0.8ms idle=1.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:55:32.230 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

11:55:32.230 [debug] QUERY OK db=0.0ms idle=11.0ms
PRAGMA foreign_key_list(comments) []

11:55:32.230 [debug] QUERY OK db=0.0ms idle=11.1ms
PRAGMA index_list(comments) []

11:55:32.230 [debug] QUERY OK db=0.0ms idle=11.2ms
PRAGMA index_info(comments_approved_index) []

11:55:32.230 [debug] QUERY OK db=0.0ms idle=11.3ms
PRAGMA index_info(comments_post_id_index) []

11:55:32.230 [debug] QUERY OK db=0.0ms idle=11.3ms
PRAGMA index_info(comments_user_id_index) []

11:55:32.231 [debug] QUERY OK db=0.0ms idle=11.4ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

11:55:32.231 [debug] QUERY OK db=0.0ms idle=12.2ms
PRAGMA table_info(comments) []

11:55:32.824 [debug] QUERY OK db=0.6ms decode=0.6ms queue=0.4ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:55:33.836 [debug] QUERY OK db=0.7ms decode=0.8ms queue=1.0ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:55:34.483 [debug] QUERY OK db=0.5ms decode=0.6ms idle=32.1ms
PRAGMA foreign_key_list(users) []

11:55:34.484 [debug] QUERY OK db=0.0ms idle=35.5ms
PRAGMA index_list(users) []

11:55:34.484 [debug] QUERY OK db=0.0ms idle=35.6ms
PRAGMA index_info(users_active_index) []

11:55:34.484 [debug] QUERY OK db=0.0ms idle=35.7ms
PRAGMA index_info(users_email_index) []

11:55:34.485 [debug] QUERY OK db=0.0ms idle=35.7ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:55:34.485 [debug] QUERY OK db=0.1ms idle=36.6ms
PRAGMA table_info(users) []

11:55:34.946 [debug] QUERY OK db=0.6ms decode=0.5ms queue=0.1ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:56:02.987 [info] Refreshing Drops.Relation cache...

11:56:02.993 [info] Repositories: [Sample.Repos.Sqlite]

11:56:02.994 [info] Tables: ["non_existent_table"]

11:56:02.994 [info] Processing repository: Sample.Repos.Sqlite

11:56:03.000 [info]   Cache cleared for Sample.Repos.Sqlite

11:56:03.001 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.non_existent_table (not cached: {:file_read, :enoent})

11:56:03.007 [debug] QUERY OK db=0.5ms decode=0.7ms idle=11.0ms
PRAGMA foreign_key_list(non_existent_table) []

11:56:03.007 [debug] QUERY OK db=0.0ms idle=12.9ms
PRAGMA index_list(non_existent_table) []

11:56:03.007 [debug] QUERY OK db=0.0ms idle=12.9ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["non_existent_table"]

11:56:03.007 [debug] QUERY OK db=0.0ms idle=13.0ms
PRAGMA table_info(non_existent_table) []

11:56:03.021 [info]   Cache warmed up for 1 tables

11:56:03.021 [info] Successful: 1

11:56:03.021 [info] Failed: 0

11:56:03.638 [info] Refreshing Drops.Relation cache...

11:56:03.641 [info] Repositories: [Sample.Repos.Sqlite]

11:56:03.641 [info] Tables: all

11:56:03.641 [info] Processing repository: Sample.Repos.Sqlite

11:56:03.644 [info]   Cache cleared for Sample.Repos.Sqlite

11:56:03.651 [debug] QUERY OK db=0.6ms decode=0.6ms idle=2.7ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:56:03.651 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

11:56:03.652 [debug] QUERY OK db=0.1ms idle=4.8ms
PRAGMA foreign_key_list(comments) []

11:56:03.652 [debug] QUERY OK db=0.0ms idle=4.6ms
PRAGMA index_list(comments) []

11:56:03.652 [debug] QUERY OK db=0.0ms idle=4.7ms
PRAGMA index_info(comments_approved_index) []

11:56:03.652 [debug] QUERY OK db=0.0ms idle=4.8ms
PRAGMA index_info(comments_post_id_index) []

11:56:03.652 [debug] QUERY OK db=0.0ms idle=4.8ms
PRAGMA index_info(comments_user_id_index) []

11:56:03.653 [debug] QUERY OK db=0.1ms idle=5.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

11:56:03.653 [debug] QUERY OK db=0.0ms idle=5.7ms
PRAGMA table_info(comments) []

11:56:03.668 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

11:56:03.668 [debug] QUERY OK db=0.0ms idle=21.0ms
PRAGMA foreign_key_list(posts) []

11:56:03.668 [debug] QUERY OK db=0.0ms idle=21.0ms
PRAGMA index_list(posts) []

11:56:03.668 [debug] QUERY OK db=0.0ms idle=18.2ms
PRAGMA index_info(posts_title_index) []

11:56:03.668 [debug] QUERY OK db=0.0ms idle=16.5ms
PRAGMA index_info(posts_published_index) []

11:56:03.668 [debug] QUERY OK db=0.0ms idle=16.5ms
PRAGMA index_info(posts_user_id_index) []

11:56:03.668 [debug] QUERY OK db=0.0ms idle=16.4ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

11:56:03.669 [debug] QUERY OK db=0.0ms idle=16.4ms
PRAGMA table_info(posts) []

11:56:03.669 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

11:56:03.669 [debug] QUERY OK db=0.0ms idle=17.1ms
PRAGMA foreign_key_list(users) []

11:56:03.669 [debug] QUERY OK db=0.0ms idle=17.1ms
PRAGMA index_list(users) []

11:56:03.669 [debug] QUERY OK db=0.0ms idle=16.4ms
PRAGMA index_info(users_active_index) []

11:56:03.669 [debug] QUERY OK db=0.0ms idle=1.2ms
PRAGMA index_info(users_email_index) []

11:56:03.669 [debug] QUERY OK db=0.0ms idle=1.2ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:56:03.670 [debug] QUERY OK db=0.0ms idle=1.2ms
PRAGMA table_info(users) []

11:56:03.671 [info]   Cache warmed up for 3 tables

11:56:03.671 [info] Successful: 1

11:56:03.671 [info] Failed: 0

11:56:04.706 [info] Refreshing Drops.Relation cache...

11:56:04.712 [info] Repositories: [Sample.Repos.Sqlite]

11:56:04.712 [info] Tables: all

11:56:04.712 [info] Processing repository: Sample.Repos.Sqlite

11:56:04.719 [info]   Cache cleared for Sample.Repos.Sqlite

11:56:04.724 [debug] QUERY OK db=0.5ms decode=0.6ms idle=5.6ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:56:04.724 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

11:56:04.725 [debug] QUERY OK db=0.0ms idle=8.1ms
PRAGMA foreign_key_list(comments) []

11:56:04.725 [debug] QUERY OK db=0.1ms idle=8.2ms
PRAGMA index_list(comments) []

11:56:04.725 [debug] QUERY OK db=0.0ms idle=8.3ms
PRAGMA index_info(comments_approved_index) []

11:56:04.725 [debug] QUERY OK db=0.0ms idle=8.4ms
PRAGMA index_info(comments_post_id_index) []

11:56:04.725 [debug] QUERY OK db=0.0ms idle=8.5ms
PRAGMA index_info(comments_user_id_index) []

11:56:04.725 [debug] QUERY OK db=0.0ms idle=8.6ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

11:56:04.726 [debug] QUERY OK db=0.0ms idle=9.1ms
PRAGMA table_info(comments) []

11:56:04.742 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

11:56:04.742 [debug] QUERY OK db=0.0ms idle=25.7ms
PRAGMA foreign_key_list(posts) []

11:56:04.742 [debug] QUERY OK db=0.0ms idle=25.9ms
PRAGMA index_list(posts) []

11:56:04.742 [debug] QUERY OK db=0.0ms idle=19.7ms
PRAGMA index_info(posts_title_index) []

11:56:04.743 [debug] QUERY OK db=0.0ms idle=17.9ms
PRAGMA index_info(posts_published_index) []

11:56:04.743 [debug] QUERY OK db=0.0ms idle=17.9ms
PRAGMA index_info(posts_user_id_index) []

11:56:04.743 [debug] QUERY OK db=0.0ms idle=17.9ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

11:56:04.743 [debug] QUERY OK db=0.0ms idle=17.9ms
PRAGMA table_info(posts) []

11:56:04.744 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

11:56:04.744 [debug] QUERY OK db=0.0ms idle=18.9ms
PRAGMA foreign_key_list(users) []

11:56:04.744 [debug] QUERY OK db=0.0ms idle=19.0ms
PRAGMA index_list(users) []

11:56:04.744 [debug] QUERY OK db=0.0ms idle=18.4ms
PRAGMA index_info(users_active_index) []

11:56:04.744 [debug] QUERY OK db=0.0ms idle=1.8ms
PRAGMA index_info(users_email_index) []

11:56:04.744 [debug] QUERY OK db=0.0ms idle=1.8ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:56:04.744 [debug] QUERY OK db=0.0ms idle=1.8ms
PRAGMA table_info(users) []

11:56:04.746 [info]   Cache warmed up for 3 tables

11:56:04.747 [info] Successful: 1

11:56:04.747 [info] Failed: 0

11:56:05.155 [info] Refreshing Drops.Relation cache...

11:56:05.161 [info] Repositories: [Sample.Repos.Sqlite]

11:56:05.162 [info] Tables: ["''"]

11:56:05.162 [info] Processing repository: Sample.Repos.Sqlite

11:56:05.169 [info]   Cache cleared for Sample.Repos.Sqlite

11:56:05.171 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.'' (not cached: {:file_read, :enoent})

11:56:05.175 [debug] QUERY OK db=0.4ms decode=0.4ms idle=10.1ms
PRAGMA foreign_key_list('') []

11:56:05.175 [debug] QUERY OK db=0.0ms idle=11.5ms
PRAGMA index_list('') []

11:56:05.175 [debug] QUERY OK db=0.0ms idle=11.6ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["''"]

11:56:05.175 [debug] QUERY OK db=0.0ms idle=11.7ms
PRAGMA table_info('') []

11:56:05.186 [info]   Cache warmed up for 1 tables

11:56:05.187 [info] Successful: 1

11:56:05.187 [info] Failed: 0

11:56:06.194 [info] Refreshing Drops.Relation cache...

11:56:06.197 [info] Repositories: [Sample.Repos.Sqlite]

11:56:06.197 [info] Tables: all

11:56:06.197 [info] Processing repository: Sample.Repos.Sqlite

11:56:06.198 [info]   Cache cleared for Sample.Repos.Sqlite

11:56:06.207 [debug] QUERY OK db=0.8ms decode=0.9ms idle=2.5ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:56:06.208 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

11:56:06.208 [debug] QUERY OK db=0.0ms idle=5.8ms
PRAGMA foreign_key_list(comments) []

11:56:06.208 [debug] QUERY OK db=0.0ms idle=6.0ms
PRAGMA index_list(comments) []

11:56:06.208 [debug] QUERY OK db=0.0ms idle=6.0ms
PRAGMA index_info(comments_approved_index) []

11:56:06.208 [debug] QUERY OK db=0.0ms idle=6.1ms
PRAGMA index_info(comments_post_id_index) []

11:56:06.208 [debug] QUERY OK db=0.0ms idle=6.2ms
PRAGMA index_info(comments_user_id_index) []

11:56:06.209 [debug] QUERY OK db=0.1ms idle=6.2ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

11:56:06.209 [debug] QUERY OK db=0.0ms idle=7.0ms
PRAGMA table_info(comments) []

11:56:06.226 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

11:56:06.227 [debug] QUERY OK db=0.0ms idle=24.5ms
PRAGMA foreign_key_list(posts) []

11:56:06.227 [debug] QUERY OK db=0.0ms idle=24.5ms
PRAGMA index_list(posts) []

11:56:06.227 [debug] QUERY OK db=0.0ms idle=21.2ms
PRAGMA index_info(posts_title_index) []

11:56:06.227 [debug] QUERY OK db=0.0ms idle=18.7ms
PRAGMA index_info(posts_published_index) []

11:56:06.227 [debug] QUERY OK db=0.0ms idle=18.7ms
PRAGMA index_info(posts_user_id_index) []

11:56:06.227 [debug] QUERY OK db=0.0ms idle=18.7ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

11:56:06.227 [debug] QUERY OK db=0.0ms idle=18.7ms
PRAGMA table_info(posts) []

11:56:06.228 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

11:56:06.228 [debug] QUERY OK db=0.0ms idle=19.6ms
PRAGMA foreign_key_list(users) []

11:56:06.228 [debug] QUERY OK db=0.0ms idle=19.5ms
PRAGMA index_list(users) []

11:56:06.228 [debug] QUERY OK db=0.0ms idle=18.7ms
PRAGMA index_info(users_active_index) []

11:56:06.228 [debug] QUERY OK db=0.0ms idle=1.4ms
PRAGMA index_info(users_email_index) []

11:56:06.228 [debug] QUERY OK db=0.0ms idle=1.4ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:56:06.228 [debug] QUERY OK db=0.0ms idle=1.4ms
PRAGMA table_info(users) []

11:56:06.229 [info]   Cache warmed up for 3 tables

11:56:06.230 [info] Successful: 1

11:56:06.230 [info] Failed: 0

11:56:06.892 [debug] QUERY OK db=1.0ms decode=0.8ms queue=1.9ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:56:06.894 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

11:56:06.894 [debug] QUERY OK db=0.0ms idle=5.9ms
PRAGMA foreign_key_list(comments) []

11:56:06.894 [debug] QUERY OK db=0.0ms idle=5.9ms
PRAGMA index_list(comments) []

11:56:06.894 [debug] QUERY OK db=0.0ms idle=6.0ms
PRAGMA index_info(comments_approved_index) []

11:56:06.894 [debug] QUERY OK db=0.0ms idle=6.0ms
PRAGMA index_info(comments_post_id_index) []

11:56:06.894 [debug] QUERY OK db=0.0ms idle=6.1ms
PRAGMA index_info(comments_user_id_index) []

11:56:06.895 [debug] QUERY OK db=0.0ms idle=6.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

11:56:06.895 [debug] QUERY OK db=0.0ms idle=6.6ms
PRAGMA table_info(comments) []

11:56:06.907 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

11:56:06.907 [debug] QUERY OK db=0.0ms idle=19.3ms
PRAGMA foreign_key_list(posts) []

11:56:06.907 [debug] QUERY OK db=0.0ms idle=19.3ms
PRAGMA index_list(posts) []

11:56:06.908 [debug] QUERY OK db=0.0ms idle=18.8ms
PRAGMA index_info(posts_title_index) []

11:56:06.908 [debug] QUERY OK db=0.0ms idle=13.8ms
PRAGMA index_info(posts_published_index) []

11:56:06.908 [debug] QUERY OK db=0.0ms idle=13.8ms
PRAGMA index_info(posts_user_id_index) []

11:56:06.908 [debug] QUERY OK db=0.0ms idle=13.7ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

11:56:06.908 [debug] QUERY OK db=0.0ms idle=13.7ms
PRAGMA table_info(posts) []

11:56:06.909 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

11:56:06.909 [debug] QUERY OK db=0.0ms idle=14.6ms
PRAGMA foreign_key_list(users) []

11:56:06.909 [debug] QUERY OK db=0.0ms idle=14.7ms
PRAGMA index_list(users) []

11:56:06.909 [debug] QUERY OK db=0.0ms idle=14.0ms
PRAGMA index_info(users_active_index) []

11:56:06.909 [debug] QUERY OK db=0.0ms idle=1.3ms
PRAGMA index_info(users_email_index) []

11:56:06.909 [debug] QUERY OK db=0.0ms idle=1.3ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:56:06.909 [debug] QUERY OK db=0.0ms idle=1.3ms
PRAGMA table_info(users) []

11:56:07.320 [info] Refreshing Drops.Relation cache...

11:56:07.328 [info] Repositories: [Sample.Repos.Sqlite]

11:56:07.329 [info] Tables: ["users", "posts"]

11:56:07.329 [info] Processing repository: Sample.Repos.Sqlite

11:56:07.334 [info]   Cache cleared for Sample.Repos.Sqlite

11:56:07.335 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

11:56:07.340 [debug] QUERY OK db=0.4ms decode=0.5ms idle=8.2ms
PRAGMA foreign_key_list(users) []

11:56:07.340 [debug] QUERY OK db=0.0ms idle=9.7ms
PRAGMA index_list(users) []

11:56:07.340 [debug] QUERY OK db=0.0ms idle=9.8ms
PRAGMA index_info(users_active_index) []

11:56:07.340 [debug] QUERY OK db=0.0ms idle=9.9ms
PRAGMA index_info(users_email_index) []

11:56:07.340 [debug] QUERY OK db=0.0ms idle=10.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:56:07.340 [debug] QUERY OK db=0.0ms idle=10.1ms
PRAGMA table_info(users) []

11:56:07.355 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

11:56:07.355 [debug] QUERY OK db=0.0ms idle=25.2ms
PRAGMA foreign_key_list(posts) []

11:56:07.355 [debug] QUERY OK db=0.0ms idle=25.3ms
PRAGMA index_list(posts) []

11:56:07.355 [debug] QUERY OK db=0.0ms idle=25.3ms
PRAGMA index_info(posts_title_index) []

11:56:07.355 [debug] QUERY OK db=0.0ms idle=25.4ms
PRAGMA index_info(posts_published_index) []

11:56:07.355 [debug] QUERY OK db=0.0ms idle=16.7ms
PRAGMA index_info(posts_user_id_index) []

11:56:07.355 [debug] QUERY OK db=0.0ms idle=15.7ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

11:56:07.356 [debug] QUERY OK db=0.0ms idle=15.7ms
PRAGMA table_info(posts) []

11:56:07.358 [info]   Cache warmed up for 2 tables

11:56:07.359 [info] Successful: 1

11:56:07.359 [info] Failed: 0

11:56:07.769 [info] Refreshing Drops.Relation cache...

11:56:07.774 [info] Repositories: [Sample.Repos.Sqlite]

11:56:07.774 [info] Tables: all

11:56:07.774 [info] Processing repository: Sample.Repos.Sqlite

11:56:07.780 [info]   Cache cleared for Sample.Repos.Sqlite

11:56:07.786 [debug] QUERY OK db=0.5ms decode=0.5ms idle=4.8ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:56:07.787 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

11:56:07.787 [debug] QUERY OK db=0.0ms idle=7.3ms
PRAGMA foreign_key_list(comments) []

11:56:07.787 [debug] QUERY OK db=0.0ms idle=7.3ms
PRAGMA index_list(comments) []

11:56:07.787 [debug] QUERY OK db=0.0ms idle=7.4ms
PRAGMA index_info(comments_approved_index) []

11:56:07.787 [debug] QUERY OK db=0.0ms idle=7.5ms
PRAGMA index_info(comments_post_id_index) []

11:56:07.787 [debug] QUERY OK db=0.0ms idle=7.5ms
PRAGMA index_info(comments_user_id_index) []

11:56:07.788 [debug] QUERY OK db=0.0ms idle=7.6ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

11:56:07.788 [debug] QUERY OK db=0.0ms idle=8.1ms
PRAGMA table_info(comments) []

11:56:07.803 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

11:56:07.803 [debug] QUERY OK db=0.0ms idle=23.5ms
PRAGMA foreign_key_list(posts) []

11:56:07.803 [debug] QUERY OK db=0.0ms idle=23.6ms
PRAGMA index_list(posts) []

11:56:07.804 [debug] QUERY OK db=0.0ms idle=18.3ms
PRAGMA index_info(posts_title_index) []

11:56:07.804 [debug] QUERY OK db=0.0ms idle=16.3ms
PRAGMA index_info(posts_published_index) []

11:56:07.804 [debug] QUERY OK db=0.0ms idle=16.3ms
PRAGMA index_info(posts_user_id_index) []

11:56:07.804 [debug] QUERY OK db=0.0ms idle=16.3ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

11:56:07.804 [debug] QUERY OK db=0.0ms idle=16.3ms
PRAGMA table_info(posts) []

11:56:07.805 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

11:56:07.805 [debug] QUERY OK db=0.0ms idle=17.3ms
PRAGMA foreign_key_list(users) []

11:56:07.805 [debug] QUERY OK db=0.0ms idle=17.3ms
PRAGMA index_list(users) []

11:56:07.805 [debug] QUERY OK db=0.0ms idle=16.7ms
PRAGMA index_info(users_active_index) []

11:56:07.805 [debug] QUERY OK db=0.0ms idle=1.3ms
PRAGMA index_info(users_email_index) []

11:56:07.805 [debug] QUERY OK db=0.0ms idle=1.3ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:56:07.805 [debug] QUERY OK db=0.0ms idle=1.3ms
PRAGMA table_info(users) []

11:56:07.806 [info]   Cache warmed up for 3 tables

11:56:07.807 [info] Successful: 1

11:56:07.807 [info] Failed: 0

11:56:17.389 [debug] QUERY OK db=0.5ms decode=0.5ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:56:19.018 [debug] QUERY OK db=0.7ms decode=0.6ms idle=36.6ms
PRAGMA foreign_key_list(users) []

11:56:19.019 [debug] QUERY OK db=0.0ms idle=40.2ms
PRAGMA index_list(users) []

11:56:19.019 [debug] QUERY OK db=0.1ms idle=40.3ms
PRAGMA index_info(users_active_index) []

11:56:19.019 [debug] QUERY OK db=0.0ms idle=40.4ms
PRAGMA index_info(users_email_index) []

11:56:19.020 [debug] QUERY OK db=0.0ms idle=40.5ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:56:19.020 [debug] QUERY OK db=0.0ms idle=41.2ms
PRAGMA table_info(users) []

11:56:19.545 [debug] QUERY OK db=0.7ms decode=0.5ms queue=2.5ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:56:20.062 [debug] QUERY OK db=0.8ms decode=0.7ms queue=1.6ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:56:20.638 [debug] QUERY OK db=0.8ms decode=0.6ms queue=1.6ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

11:56:21.276 [debug] QUERY OK db=0.8ms decode=0.6ms idle=30.2ms
PRAGMA foreign_key_list(users) []

11:56:21.277 [debug] QUERY OK db=0.1ms idle=34.1ms
PRAGMA index_list(users) []

11:56:21.277 [debug] QUERY OK db=0.0ms idle=34.3ms
PRAGMA index_info(users_active_index) []

11:56:21.278 [debug] QUERY OK db=0.1ms idle=34.4ms
PRAGMA index_info(users_email_index) []

11:56:21.279 [debug] QUERY OK db=0.2ms idle=34.6ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

11:56:21.279 [debug] QUERY OK db=0.1ms idle=36.0ms
PRAGMA table_info(users) []

12:21:28.215 [debug] QUERY OK db=0.6ms decode=0.7ms queue=0.1ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

12:21:29.225 [debug] QUERY OK db=0.5ms decode=0.4ms queue=1.1ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

12:21:29.856 [debug] QUERY OK db=0.5ms decode=0.5ms idle=30.6ms
PRAGMA foreign_key_list(users) []

12:21:29.857 [debug] QUERY OK db=0.0ms idle=33.8ms
PRAGMA index_list(users) []

12:21:29.857 [debug] QUERY OK db=0.0ms idle=33.9ms
PRAGMA index_info(users_active_index) []

12:21:29.857 [debug] QUERY OK db=0.0ms idle=34.0ms
PRAGMA index_info(users_email_index) []

12:21:29.859 [debug] QUERY OK db=0.0ms idle=34.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

12:21:29.859 [debug] QUERY OK db=0.0ms idle=35.3ms
PRAGMA table_info(users) []

12:21:30.362 [debug] QUERY OK db=0.9ms decode=0.7ms queue=1.2ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

12:21:30.943 [debug] QUERY OK db=0.6ms decode=0.5ms queue=0.8ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

12:21:32.018 [debug] QUERY OK db=0.7ms decode=0.6ms idle=36.0ms
PRAGMA foreign_key_list(users) []

12:21:32.019 [debug] QUERY OK db=0.0ms idle=40.1ms
PRAGMA index_list(users) []

12:21:32.019 [debug] QUERY OK db=0.0ms idle=40.2ms
PRAGMA index_info(users_active_index) []

12:21:32.019 [debug] QUERY OK db=0.0ms idle=40.3ms
PRAGMA index_info(users_email_index) []

12:21:32.020 [debug] QUERY OK db=0.0ms idle=40.3ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

12:21:32.020 [debug] QUERY OK db=0.0ms idle=41.3ms
PRAGMA table_info(users) []

12:21:34.318 [info] Refreshing Drops.Relation cache...

12:21:34.324 [info] Repositories: [Sample.Repos.Sqlite]

12:21:34.325 [info] Tables: ["non_existent_table"]

12:21:34.325 [info] Processing repository: Sample.Repos.Sqlite

12:21:34.332 [info]   Cache cleared for Sample.Repos.Sqlite

12:21:34.334 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.non_existent_table (not cached: {:file_read, :enoent})

12:21:34.340 [debug] QUERY OK db=0.6ms decode=0.7ms idle=11.3ms
PRAGMA foreign_key_list(non_existent_table) []

12:21:34.340 [debug] QUERY OK db=0.0ms idle=12.8ms
PRAGMA index_list(non_existent_table) []

12:21:34.341 [debug] QUERY OK db=0.0ms idle=12.9ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["non_existent_table"]

12:21:34.341 [debug] QUERY OK db=0.0ms idle=12.9ms
PRAGMA table_info(non_existent_table) []

12:21:34.355 [info]   Cache warmed up for 1 tables

12:21:34.357 [info] Successful: 1

12:21:34.357 [info] Failed: 0

12:21:35.027 [info] Refreshing Drops.Relation cache...

12:21:35.029 [info] Repositories: [Sample.Repos.Sqlite]

12:21:35.029 [info] Tables: all

12:21:35.029 [info] Processing repository: Sample.Repos.Sqlite

12:21:35.031 [info]   Cache cleared for Sample.Repos.Sqlite

12:21:35.041 [debug] QUERY OK db=0.7ms decode=0.8ms idle=2.1ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

12:21:35.041 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

12:21:35.041 [debug] QUERY OK db=0.0ms idle=5.0ms
PRAGMA foreign_key_list(comments) []

12:21:35.042 [debug] QUERY OK db=0.0ms idle=5.1ms
PRAGMA index_list(comments) []

12:21:35.042 [debug] QUERY OK db=0.0ms idle=5.1ms
PRAGMA index_info(comments_approved_index) []

12:21:35.042 [debug] QUERY OK db=0.0ms idle=5.3ms
PRAGMA index_info(comments_post_id_index) []

12:21:35.042 [debug] QUERY OK db=0.0ms idle=5.4ms
PRAGMA index_info(comments_user_id_index) []

12:21:35.043 [debug] QUERY OK db=0.0ms idle=5.4ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

12:21:35.043 [debug] QUERY OK db=0.0ms idle=6.3ms
PRAGMA table_info(comments) []

12:21:35.061 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

12:21:35.062 [debug] QUERY OK db=0.0ms idle=25.0ms
PRAGMA foreign_key_list(posts) []

12:21:35.062 [debug] QUERY OK db=0.0ms idle=25.0ms
PRAGMA index_list(posts) []

12:21:35.062 [debug] QUERY OK db=0.0ms idle=22.4ms
PRAGMA index_info(posts_title_index) []

12:21:35.062 [debug] QUERY OK db=0.0ms idle=20.3ms
PRAGMA index_info(posts_published_index) []

12:21:35.062 [debug] QUERY OK db=0.0ms idle=20.2ms
PRAGMA index_info(posts_user_id_index) []

12:21:35.062 [debug] QUERY OK db=0.0ms idle=20.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

12:21:35.062 [debug] QUERY OK db=0.1ms idle=20.2ms
PRAGMA table_info(posts) []

12:21:35.063 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

12:21:35.063 [debug] QUERY OK db=0.0ms idle=21.4ms
PRAGMA foreign_key_list(users) []

12:21:35.063 [debug] QUERY OK db=0.0ms idle=21.4ms
PRAGMA index_list(users) []

12:21:35.064 [debug] QUERY OK db=0.0ms idle=20.5ms
PRAGMA index_info(users_active_index) []

12:21:35.064 [debug] QUERY OK db=0.0ms idle=1.9ms
PRAGMA index_info(users_email_index) []

12:21:35.064 [debug] QUERY OK db=0.0ms idle=1.9ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

12:21:35.064 [debug] QUERY OK db=0.0ms idle=1.9ms
PRAGMA table_info(users) []

12:21:35.066 [info]   Cache warmed up for 3 tables

12:21:35.066 [info] Successful: 1

12:21:35.066 [info] Failed: 0

12:21:35.725 [debug] QUERY OK db=0.7ms decode=0.7ms queue=0.5ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

12:21:35.727 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

12:21:35.727 [debug] QUERY OK db=0.0ms idle=6.2ms
PRAGMA foreign_key_list(comments) []

12:21:35.727 [debug] QUERY OK db=0.0ms idle=6.3ms
PRAGMA index_list(comments) []

12:21:35.727 [debug] QUERY OK db=0.0ms idle=6.3ms
PRAGMA index_info(comments_approved_index) []

12:21:35.727 [debug] QUERY OK db=0.0ms idle=6.4ms
PRAGMA index_info(comments_post_id_index) []

12:21:35.727 [debug] QUERY OK db=0.0ms idle=6.4ms
PRAGMA index_info(comments_user_id_index) []

12:21:35.728 [debug] QUERY OK db=0.0ms idle=6.5ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

12:21:35.728 [debug] QUERY OK db=0.1ms idle=7.2ms
PRAGMA table_info(comments) []

12:21:35.740 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

12:21:35.740 [debug] QUERY OK db=0.0ms idle=19.1ms
PRAGMA foreign_key_list(posts) []

12:21:35.740 [debug] QUERY OK db=0.0ms idle=19.2ms
PRAGMA index_list(posts) []

12:21:35.740 [debug] QUERY OK db=0.0ms idle=18.5ms
PRAGMA index_info(posts_title_index) []

12:21:35.740 [debug] QUERY OK db=0.0ms idle=13.0ms
PRAGMA index_info(posts_published_index) []

12:21:35.740 [debug] QUERY OK db=0.0ms idle=13.0ms
PRAGMA index_info(posts_user_id_index) []

12:21:35.740 [debug] QUERY OK db=0.0ms idle=13.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

12:21:35.740 [debug] QUERY OK db=0.0ms idle=13.0ms
PRAGMA table_info(posts) []

12:21:35.741 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

12:21:35.741 [debug] QUERY OK db=0.0ms idle=13.8ms
PRAGMA foreign_key_list(users) []

12:21:35.741 [debug] QUERY OK db=0.0ms idle=13.8ms
PRAGMA index_list(users) []

12:21:35.741 [debug] QUERY OK db=0.0ms idle=13.0ms
PRAGMA index_info(users_active_index) []

12:21:35.741 [debug] QUERY OK db=0.0ms idle=1.2ms
PRAGMA index_info(users_email_index) []

12:21:35.741 [debug] QUERY OK db=0.0ms idle=1.2ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

12:21:35.741 [debug] QUERY OK db=0.0ms idle=1.2ms
PRAGMA table_info(users) []

12:21:36.130 [info] Refreshing Drops.Relation cache...

12:21:36.136 [info] Repositories: [Sample.Repos.Sqlite]

12:21:36.136 [info] Tables: all

12:21:36.136 [info] Processing repository: Sample.Repos.Sqlite

12:21:36.141 [info]   Cache cleared for Sample.Repos.Sqlite

12:21:36.146 [debug] QUERY OK db=0.5ms decode=0.5ms idle=7.3ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

12:21:36.146 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

12:21:36.147 [debug] QUERY OK db=0.0ms idle=9.4ms
PRAGMA foreign_key_list(comments) []

12:21:36.147 [debug] QUERY OK db=0.0ms idle=9.5ms
PRAGMA index_list(comments) []

12:21:36.147 [debug] QUERY OK db=0.0ms idle=9.6ms
PRAGMA index_info(comments_approved_index) []

12:21:36.147 [debug] QUERY OK db=0.0ms idle=8.9ms
PRAGMA index_info(comments_post_id_index) []

12:21:36.147 [debug] QUERY OK db=0.0ms idle=8.9ms
PRAGMA index_info(comments_user_id_index) []

12:21:36.147 [debug] QUERY OK db=0.0ms idle=8.9ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

12:21:36.147 [debug] QUERY OK db=0.0ms idle=9.6ms
PRAGMA table_info(comments) []

12:21:36.163 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

12:21:36.163 [debug] QUERY OK db=0.0ms idle=25.4ms
PRAGMA foreign_key_list(posts) []

12:21:36.163 [debug] QUERY OK db=0.0ms idle=25.4ms
PRAGMA index_list(posts) []

12:21:36.163 [debug] QUERY OK db=0.0ms idle=18.5ms
PRAGMA index_info(posts_title_index) []

12:21:36.163 [debug] QUERY OK db=0.0ms idle=16.8ms
PRAGMA index_info(posts_published_index) []

12:21:36.164 [debug] QUERY OK db=0.0ms idle=16.8ms
PRAGMA index_info(posts_user_id_index) []

12:21:36.164 [debug] QUERY OK db=0.0ms idle=16.8ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

12:21:36.164 [debug] QUERY OK db=0.0ms idle=16.8ms
PRAGMA table_info(posts) []

12:21:36.164 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

12:21:36.164 [debug] QUERY OK db=0.0ms idle=17.5ms
PRAGMA foreign_key_list(users) []

12:21:36.164 [debug] QUERY OK db=0.0ms idle=17.5ms
PRAGMA index_list(users) []

12:21:36.164 [debug] QUERY OK db=0.0ms idle=16.9ms
PRAGMA index_info(users_active_index) []

12:21:36.165 [debug] QUERY OK db=0.0ms idle=1.1ms
PRAGMA index_info(users_email_index) []

12:21:36.165 [debug] QUERY OK db=0.0ms idle=1.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

12:21:36.165 [debug] QUERY OK db=0.0ms idle=1.1ms
PRAGMA table_info(users) []

12:21:36.166 [info]   Cache warmed up for 3 tables

12:21:36.167 [info] Successful: 1

12:21:36.167 [info] Failed: 0

12:21:36.969 [info] Refreshing Drops.Relation cache...

12:21:36.975 [info] Repositories: [Sample.Repos.Sqlite]

12:21:36.977 [info] Tables: ["users", "posts"]

12:21:36.977 [info] Processing repository: Sample.Repos.Sqlite

12:21:36.983 [info]   Cache cleared for Sample.Repos.Sqlite

12:21:36.983 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

12:21:36.988 [debug] QUERY OK db=0.5ms decode=0.5ms idle=7.8ms
PRAGMA foreign_key_list(users) []

12:21:36.988 [debug] QUERY OK db=0.0ms idle=9.5ms
PRAGMA index_list(users) []

12:21:36.988 [debug] QUERY OK db=0.0ms idle=9.5ms
PRAGMA index_info(users_active_index) []

12:21:36.988 [debug] QUERY OK db=0.0ms idle=9.6ms
PRAGMA index_info(users_email_index) []

12:21:36.989 [debug] QUERY OK db=0.0ms idle=9.6ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

12:21:36.989 [debug] QUERY OK db=0.0ms idle=9.7ms
PRAGMA table_info(users) []

12:21:37.006 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

12:21:37.007 [debug] QUERY OK db=0.1ms idle=27.5ms
PRAGMA foreign_key_list(posts) []

12:21:37.007 [debug] QUERY OK db=0.0ms idle=27.7ms
PRAGMA index_list(posts) []

12:21:37.007 [debug] QUERY OK db=0.0ms idle=27.8ms
PRAGMA index_info(posts_title_index) []

12:21:37.007 [debug] QUERY OK db=0.0ms idle=27.9ms
PRAGMA index_info(posts_published_index) []

12:21:37.007 [debug] QUERY OK db=0.0ms idle=19.7ms
PRAGMA index_info(posts_user_id_index) []

12:21:37.007 [debug] QUERY OK db=0.0ms idle=18.6ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

12:21:37.007 [debug] QUERY OK db=0.1ms idle=18.6ms
PRAGMA table_info(posts) []

12:21:37.009 [info]   Cache warmed up for 2 tables

12:21:37.011 [info] Successful: 1

12:21:37.011 [info] Failed: 0

12:21:37.730 [info] Refreshing Drops.Relation cache...

12:21:37.737 [info] Repositories: [Sample.Repos.Sqlite]

12:21:37.737 [info] Tables: all

12:21:37.738 [info] Processing repository: Sample.Repos.Sqlite

12:21:37.744 [info]   Cache cleared for Sample.Repos.Sqlite

12:21:37.748 [debug] QUERY OK db=0.5ms decode=0.4ms idle=9.1ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

12:21:37.749 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

12:21:37.749 [debug] QUERY OK db=0.1ms idle=11.3ms
PRAGMA foreign_key_list(comments) []

12:21:37.749 [debug] QUERY OK db=0.0ms idle=11.5ms
PRAGMA index_list(comments) []

12:21:37.749 [debug] QUERY OK db=0.0ms idle=11.5ms
PRAGMA index_info(comments_approved_index) []

12:21:37.749 [debug] QUERY OK db=0.0ms idle=11.6ms
PRAGMA index_info(comments_post_id_index) []

12:21:37.749 [debug] QUERY OK db=0.0ms idle=11.7ms
PRAGMA index_info(comments_user_id_index) []

12:21:37.750 [debug] QUERY OK db=0.0ms idle=11.7ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

12:21:37.750 [debug] QUERY OK db=0.0ms idle=12.3ms
PRAGMA table_info(comments) []

12:21:37.766 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

12:21:37.767 [debug] QUERY OK db=0.0ms idle=28.7ms
PRAGMA foreign_key_list(posts) []

12:21:37.767 [debug] QUERY OK db=0.0ms idle=28.8ms
PRAGMA index_list(posts) []

12:21:37.767 [debug] QUERY OK db=0.0ms idle=19.4ms
PRAGMA index_info(posts_title_index) []

12:21:37.767 [debug] QUERY OK db=0.0ms idle=17.6ms
PRAGMA index_info(posts_published_index) []

12:21:37.767 [debug] QUERY OK db=0.0ms idle=17.5ms
PRAGMA index_info(posts_user_id_index) []

12:21:37.767 [debug] QUERY OK db=0.0ms idle=17.5ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

12:21:37.767 [debug] QUERY OK db=0.0ms idle=17.4ms
PRAGMA table_info(posts) []

12:21:37.768 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

12:21:37.768 [debug] QUERY OK db=0.0ms idle=18.1ms
PRAGMA foreign_key_list(users) []

12:21:37.768 [debug] QUERY OK db=0.0ms idle=18.0ms
PRAGMA index_list(users) []

12:21:37.768 [debug] QUERY OK db=0.0ms idle=17.5ms
PRAGMA index_info(users_active_index) []

12:21:37.768 [debug] QUERY OK db=0.0ms idle=1.2ms
PRAGMA index_info(users_email_index) []

12:21:37.768 [debug] QUERY OK db=0.0ms idle=1.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

12:21:37.768 [debug] QUERY OK db=0.0ms idle=1.2ms
PRAGMA table_info(users) []

12:21:37.769 [info]   Cache warmed up for 3 tables

12:21:37.770 [info] Successful: 1

12:21:37.770 [info] Failed: 0

12:21:38.166 [info] Refreshing Drops.Relation cache...

12:21:38.171 [info] Repositories: [Sample.Repos.Sqlite]

12:21:38.172 [info] Tables: ["''"]

12:21:38.172 [info] Processing repository: Sample.Repos.Sqlite

12:21:38.179 [info]   Cache cleared for Sample.Repos.Sqlite

12:21:38.180 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.'' (not cached: {:file_read, :enoent})

12:21:38.185 [debug] QUERY OK db=0.4ms decode=0.7ms idle=10.4ms
PRAGMA foreign_key_list('') []

12:21:38.185 [debug] QUERY OK db=0.0ms idle=12.1ms
PRAGMA index_list('') []

12:21:38.185 [debug] QUERY OK db=0.0ms idle=12.2ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["''"]

12:21:38.185 [debug] QUERY OK db=0.0ms idle=12.3ms
PRAGMA table_info('') []

12:21:38.197 [info]   Cache warmed up for 1 tables

12:21:38.198 [info] Successful: 1

12:21:38.198 [info] Failed: 0

12:21:38.788 [info] Refreshing Drops.Relation cache...

12:21:38.790 [info] Repositories: [Sample.Repos.Sqlite]

12:21:38.790 [info] Tables: all

12:21:38.790 [info] Processing repository: Sample.Repos.Sqlite

12:21:38.791 [info]   Cache cleared for Sample.Repos.Sqlite

12:21:38.800 [debug] QUERY OK db=0.6ms decode=0.6ms idle=1.6ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

12:21:38.800 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

12:21:38.800 [debug] QUERY OK db=0.0ms idle=3.8ms
PRAGMA foreign_key_list(comments) []

12:21:38.800 [debug] QUERY OK db=0.0ms idle=3.9ms
PRAGMA index_list(comments) []

12:21:38.800 [debug] QUERY OK db=0.0ms idle=3.9ms
PRAGMA index_info(comments_approved_index) []

12:21:38.800 [debug] QUERY OK db=0.0ms idle=4.0ms
PRAGMA index_info(comments_post_id_index) []

12:21:38.800 [debug] QUERY OK db=0.0ms idle=4.0ms
PRAGMA index_info(comments_user_id_index) []

12:21:38.801 [debug] QUERY OK db=0.0ms idle=4.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

12:21:38.801 [debug] QUERY OK db=0.0ms idle=4.7ms
PRAGMA table_info(comments) []

12:21:38.818 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

12:21:38.818 [debug] QUERY OK db=0.0ms idle=22.1ms
PRAGMA foreign_key_list(posts) []

12:21:38.818 [debug] QUERY OK db=0.0ms idle=22.2ms
PRAGMA index_list(posts) []

12:21:38.819 [debug] QUERY OK db=0.0ms idle=20.0ms
PRAGMA index_info(posts_title_index) []

12:21:38.819 [debug] QUERY OK db=0.0ms idle=18.5ms
PRAGMA index_info(posts_published_index) []

12:21:38.819 [debug] QUERY OK db=0.0ms idle=18.5ms
PRAGMA index_info(posts_user_id_index) []

12:21:38.819 [debug] QUERY OK db=0.0ms idle=18.6ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

12:21:38.819 [debug] QUERY OK db=0.0ms idle=18.6ms
PRAGMA table_info(posts) []

12:21:38.820 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

12:21:38.820 [debug] QUERY OK db=0.0ms idle=19.4ms
PRAGMA foreign_key_list(users) []

12:21:38.820 [debug] QUERY OK db=0.0ms idle=19.4ms
PRAGMA index_list(users) []

12:21:38.820 [debug] QUERY OK db=0.0ms idle=18.9ms
PRAGMA index_info(users_active_index) []

12:21:38.820 [debug] QUERY OK db=0.0ms idle=1.4ms
PRAGMA index_info(users_email_index) []

12:21:38.820 [debug] QUERY OK db=0.0ms idle=1.4ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

12:21:38.820 [debug] QUERY OK db=0.0ms idle=1.4ms
PRAGMA table_info(users) []

12:21:38.821 [info]   Cache warmed up for 3 tables

12:21:38.821 [info] Successful: 1

12:21:38.821 [info] Failed: 0

12:22:24.778 [info] Refreshing Drops.Relation cache...

12:22:24.783 [info] Repositories: [Sample.Repos.Sqlite]

12:22:24.785 [info] Tables: ["non_existent_table"]

12:22:24.785 [info] Processing repository: Sample.Repos.Sqlite

12:22:24.786 [info]   Cache cleared for Sample.Repos.Sqlite

12:22:24.789 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.non_existent_table (not cached: {:file_read, :enoent})

12:22:24.794 [debug] QUERY OK db=0.4ms decode=0.5ms idle=2.8ms
PRAGMA foreign_key_list(non_existent_table) []

12:22:24.794 [debug] QUERY OK db=0.0ms idle=4.3ms
PRAGMA index_list(non_existent_table) []

12:22:24.794 [debug] QUERY OK db=0.0ms idle=4.4ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["non_existent_table"]

12:22:24.794 [debug] QUERY OK db=0.0ms idle=4.5ms
PRAGMA table_info(non_existent_table) []

12:22:24.803 [info]   Cache warmed up for 1 tables

12:22:24.803 [info] Successful: 1

12:22:24.803 [info] Failed: 0

12:22:25.424 [info] Refreshing Drops.Relation cache...

12:22:25.426 [info] Repositories: [Sample.Repos.Sqlite]

12:22:25.426 [info] Tables: all

12:22:25.426 [info] Processing repository: Sample.Repos.Sqlite

12:22:25.428 [info]   Cache cleared for Sample.Repos.Sqlite

12:22:25.435 [debug] QUERY OK db=0.6ms decode=0.6ms idle=1.8ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

12:22:25.436 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

12:22:25.436 [debug] QUERY OK db=0.0ms idle=4.2ms
PRAGMA foreign_key_list(comments) []

12:22:25.436 [debug] QUERY OK db=0.0ms idle=4.3ms
PRAGMA index_list(comments) []

12:22:25.436 [debug] QUERY OK db=0.0ms idle=4.3ms
PRAGMA index_info(comments_approved_index) []

12:22:25.436 [debug] QUERY OK db=0.0ms idle=4.4ms
PRAGMA index_info(comments_post_id_index) []

12:22:25.436 [debug] QUERY OK db=0.0ms idle=4.4ms
PRAGMA index_info(comments_user_id_index) []

12:22:25.437 [debug] QUERY OK db=0.0ms idle=4.5ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

12:22:25.437 [debug] QUERY OK db=0.1ms idle=5.4ms
PRAGMA table_info(comments) []

12:22:25.453 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

12:22:25.453 [debug] QUERY OK db=0.0ms idle=21.3ms
PRAGMA foreign_key_list(posts) []

12:22:25.453 [debug] QUERY OK db=0.0ms idle=21.3ms
PRAGMA index_list(posts) []

12:22:25.453 [debug] QUERY OK db=0.0ms idle=18.9ms
PRAGMA index_info(posts_title_index) []

12:22:25.453 [debug] QUERY OK db=0.0ms idle=17.3ms
PRAGMA index_info(posts_published_index) []

12:22:25.453 [debug] QUERY OK db=0.0ms idle=17.3ms
PRAGMA index_info(posts_user_id_index) []

12:22:25.453 [debug] QUERY OK db=0.0ms idle=17.3ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

12:22:25.453 [debug] QUERY OK db=0.0ms idle=17.3ms
PRAGMA table_info(posts) []

12:22:25.454 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

12:22:25.454 [debug] QUERY OK db=0.0ms idle=18.2ms
PRAGMA foreign_key_list(users) []

12:22:25.455 [debug] QUERY OK db=0.0ms idle=18.2ms
PRAGMA index_list(users) []

12:22:25.455 [debug] QUERY OK db=0.0ms idle=17.3ms
PRAGMA index_info(users_active_index) []

12:22:25.455 [debug] QUERY OK db=0.0ms idle=1.6ms
PRAGMA index_info(users_email_index) []

12:22:25.455 [debug] QUERY OK db=0.0ms idle=1.6ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

12:22:25.455 [debug] QUERY OK db=0.1ms idle=1.6ms
PRAGMA table_info(users) []

12:22:25.457 [info]   Cache warmed up for 3 tables

12:22:25.457 [info] Successful: 1

12:22:25.457 [info] Failed: 0

12:22:26.094 [info] Refreshing Drops.Relation cache...

12:22:26.098 [info] Repositories: [Sample.Repos.Sqlite]

12:22:26.098 [info] Tables: all

12:22:26.098 [info] Processing repository: Sample.Repos.Sqlite

12:22:26.101 [info]   Cache cleared for Sample.Repos.Sqlite

12:22:26.108 [debug] QUERY OK db=0.7ms decode=0.6ms idle=2.5ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

12:22:26.109 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

12:22:26.109 [debug] QUERY OK db=0.0ms idle=5.4ms
PRAGMA foreign_key_list(comments) []

12:22:26.109 [debug] QUERY OK db=0.0ms idle=5.5ms
PRAGMA index_list(comments) []

12:22:26.109 [debug] QUERY OK db=0.0ms idle=5.6ms
PRAGMA index_info(comments_approved_index) []

12:22:26.109 [debug] QUERY OK db=0.0ms idle=5.7ms
PRAGMA index_info(comments_post_id_index) []

12:22:26.109 [debug] QUERY OK db=0.0ms idle=5.7ms
PRAGMA index_info(comments_user_id_index) []

12:22:26.110 [debug] QUERY OK db=0.0ms idle=5.8ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

12:22:26.110 [debug] QUERY OK db=0.0ms idle=6.4ms
PRAGMA table_info(comments) []

12:22:26.123 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

12:22:26.123 [debug] QUERY OK db=0.0ms idle=19.4ms
PRAGMA foreign_key_list(posts) []

12:22:26.123 [debug] QUERY OK db=0.0ms idle=19.5ms
PRAGMA index_list(posts) []

12:22:26.123 [debug] QUERY OK db=0.0ms idle=16.3ms
PRAGMA index_info(posts_title_index) []

12:22:26.123 [debug] QUERY OK db=0.0ms idle=14.1ms
PRAGMA index_info(posts_published_index) []

12:22:26.123 [debug] QUERY OK db=0.0ms idle=14.1ms
PRAGMA index_info(posts_user_id_index) []

12:22:26.123 [debug] QUERY OK db=0.0ms idle=14.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

12:22:26.123 [debug] QUERY OK db=0.0ms idle=14.0ms
PRAGMA table_info(posts) []

12:22:26.124 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

12:22:26.124 [debug] QUERY OK db=0.0ms idle=14.9ms
PRAGMA foreign_key_list(users) []

12:22:26.124 [debug] QUERY OK db=0.0ms idle=14.9ms
PRAGMA index_list(users) []

12:22:26.124 [debug] QUERY OK db=0.0ms idle=14.3ms
PRAGMA index_info(users_active_index) []

12:22:26.124 [debug] QUERY OK db=0.0ms idle=1.3ms
PRAGMA index_info(users_email_index) []

12:22:26.125 [debug] QUERY OK db=0.0ms idle=1.3ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

12:22:26.125 [debug] QUERY OK db=0.0ms idle=1.3ms
PRAGMA table_info(users) []

12:22:26.126 [info]   Cache warmed up for 3 tables

12:22:26.126 [info] Successful: 1

12:22:26.126 [info] Failed: 0

12:22:27.174 [info] Refreshing Drops.Relation cache...

12:22:27.180 [info] Repositories: [Sample.Repos.Sqlite]

12:22:27.180 [info] Tables: all

12:22:27.180 [info] Processing repository: Sample.Repos.Sqlite

12:22:27.186 [info]   Cache cleared for Sample.Repos.Sqlite

12:22:27.191 [debug] QUERY OK db=0.5ms decode=0.6ms idle=4.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

12:22:27.191 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

12:22:27.191 [debug] QUERY OK db=0.0ms idle=6.4ms
PRAGMA foreign_key_list(comments) []

12:22:27.192 [debug] QUERY OK db=0.0ms idle=6.4ms
PRAGMA index_list(comments) []

12:22:27.192 [debug] QUERY OK db=0.0ms idle=6.5ms
PRAGMA index_info(comments_approved_index) []

12:22:27.192 [debug] QUERY OK db=0.0ms idle=6.6ms
PRAGMA index_info(comments_post_id_index) []

12:22:27.192 [debug] QUERY OK db=0.0ms idle=6.6ms
PRAGMA index_info(comments_user_id_index) []

12:22:27.192 [debug] QUERY OK db=0.0ms idle=6.6ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

12:22:27.192 [debug] QUERY OK db=0.0ms idle=7.2ms
PRAGMA table_info(comments) []

12:22:27.212 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

12:22:27.212 [debug] QUERY OK db=0.0ms idle=27.2ms
PRAGMA foreign_key_list(posts) []

12:22:27.212 [debug] QUERY OK db=0.0ms idle=27.2ms
PRAGMA index_list(posts) []

12:22:27.212 [debug] QUERY OK db=0.0ms idle=22.8ms
PRAGMA index_info(posts_title_index) []

12:22:27.213 [debug] QUERY OK db=0.0ms idle=21.0ms
PRAGMA index_info(posts_published_index) []

12:22:27.213 [debug] QUERY OK db=0.0ms idle=21.0ms
PRAGMA index_info(posts_user_id_index) []

12:22:27.213 [debug] QUERY OK db=0.0ms idle=21.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

12:22:27.213 [debug] QUERY OK db=0.0ms idle=21.0ms
PRAGMA table_info(posts) []

12:22:27.213 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

12:22:27.214 [debug] QUERY OK db=0.0ms idle=21.8ms
PRAGMA foreign_key_list(users) []

12:22:27.214 [debug] QUERY OK db=0.0ms idle=21.8ms
PRAGMA index_list(users) []

12:22:27.214 [debug] QUERY OK db=0.0ms idle=21.3ms
PRAGMA index_info(users_active_index) []

12:22:27.214 [debug] QUERY OK db=0.0ms idle=1.3ms
PRAGMA index_info(users_email_index) []

12:22:27.214 [debug] QUERY OK db=0.0ms idle=1.3ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

12:22:27.214 [debug] QUERY OK db=0.0ms idle=1.3ms
PRAGMA table_info(users) []

12:22:27.215 [info]   Cache warmed up for 3 tables

12:22:27.216 [info] Successful: 1

12:22:27.216 [info] Failed: 0

12:22:27.862 [info] Refreshing Drops.Relation cache...

12:22:27.867 [info] Repositories: [Sample.Repos.Sqlite]

12:22:27.868 [info] Tables: ["users", "posts"]

12:22:27.868 [info] Processing repository: Sample.Repos.Sqlite

12:22:27.871 [info]   Cache cleared for Sample.Repos.Sqlite

12:22:27.873 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

12:22:27.877 [debug] QUERY OK db=0.4ms decode=0.4ms idle=5.4ms
PRAGMA foreign_key_list(users) []

12:22:27.877 [debug] QUERY OK db=0.0ms idle=6.8ms
PRAGMA index_list(users) []

12:22:27.877 [debug] QUERY OK db=0.0ms idle=6.9ms
PRAGMA index_info(users_active_index) []

12:22:27.878 [debug] QUERY OK db=0.0ms idle=6.9ms
PRAGMA index_info(users_email_index) []

12:22:27.878 [debug] QUERY OK db=0.0ms idle=7.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

12:22:27.878 [debug] QUERY OK db=0.0ms idle=7.0ms
PRAGMA table_info(users) []

12:22:27.888 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

12:22:27.888 [debug] QUERY OK db=0.0ms idle=17.7ms
PRAGMA foreign_key_list(posts) []

12:22:27.888 [debug] QUERY OK db=0.0ms idle=17.7ms
PRAGMA index_list(posts) []

12:22:27.888 [debug] QUERY OK db=0.0ms idle=17.7ms
PRAGMA index_info(posts_title_index) []

12:22:27.889 [debug] QUERY OK db=0.0ms idle=17.6ms
PRAGMA index_info(posts_published_index) []

12:22:27.889 [debug] QUERY OK db=0.0ms idle=12.1ms
PRAGMA index_info(posts_user_id_index) []

12:22:27.889 [debug] QUERY OK db=0.0ms idle=11.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

12:22:27.889 [debug] QUERY OK db=0.0ms idle=11.1ms
PRAGMA table_info(posts) []

12:22:27.890 [info]   Cache warmed up for 2 tables

12:22:27.890 [info] Successful: 1

12:22:27.890 [info] Failed: 0

12:22:28.683 [info] Refreshing Drops.Relation cache...

12:22:28.689 [info] Repositories: [Sample.Repos.Sqlite]

12:22:28.689 [info] Tables: all

12:22:28.689 [info] Processing repository: Sample.Repos.Sqlite

12:22:28.695 [info]   Cache cleared for Sample.Repos.Sqlite

12:22:28.701 [debug] QUERY OK db=0.5ms decode=0.5ms idle=5.2ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

12:22:28.702 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

12:22:28.702 [debug] QUERY OK db=0.0ms idle=7.9ms
PRAGMA foreign_key_list(comments) []

12:22:28.702 [debug] QUERY OK db=0.0ms idle=8.0ms
PRAGMA index_list(comments) []

12:22:28.702 [debug] QUERY OK db=0.0ms idle=8.1ms
PRAGMA index_info(comments_approved_index) []

12:22:28.703 [debug] QUERY OK db=0.0ms idle=8.2ms
PRAGMA index_info(comments_post_id_index) []

12:22:28.703 [debug] QUERY OK db=0.0ms idle=8.3ms
PRAGMA index_info(comments_user_id_index) []

12:22:28.704 [debug] QUERY OK db=0.0ms idle=8.4ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

12:22:28.704 [debug] QUERY OK db=0.0ms idle=9.2ms
PRAGMA table_info(comments) []

12:22:28.719 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

12:22:28.719 [debug] QUERY OK db=0.0ms idle=25.0ms
PRAGMA foreign_key_list(posts) []

12:22:28.719 [debug] QUERY OK db=0.0ms idle=25.0ms
PRAGMA index_list(posts) []

12:22:28.720 [debug] QUERY OK db=0.0ms idle=19.4ms
PRAGMA index_info(posts_title_index) []

12:22:28.720 [debug] QUERY OK db=0.0ms idle=17.2ms
PRAGMA index_info(posts_published_index) []

12:22:28.720 [debug] QUERY OK db=0.0ms idle=17.2ms
PRAGMA index_info(posts_user_id_index) []

12:22:28.720 [debug] QUERY OK db=0.0ms idle=17.2ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

12:22:28.720 [debug] QUERY OK db=0.1ms idle=17.2ms
PRAGMA table_info(posts) []

12:22:28.721 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

12:22:28.721 [debug] QUERY OK db=0.0ms idle=18.2ms
PRAGMA foreign_key_list(users) []

12:22:28.721 [debug] QUERY OK db=0.0ms idle=18.2ms
PRAGMA index_list(users) []

12:22:28.721 [debug] QUERY OK db=0.0ms idle=17.4ms
PRAGMA index_info(users_active_index) []

12:22:28.721 [debug] QUERY OK db=0.0ms idle=1.7ms
PRAGMA index_info(users_email_index) []

12:22:28.721 [debug] QUERY OK db=0.0ms idle=1.7ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

12:22:28.721 [debug] QUERY OK db=0.0ms idle=1.7ms
PRAGMA table_info(users) []

12:22:28.722 [info]   Cache warmed up for 3 tables

12:22:28.723 [info] Successful: 1

12:22:28.723 [info] Failed: 0

12:22:29.161 [debug] QUERY OK db=0.5ms decode=0.4ms idle=1.8ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

12:22:29.163 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

12:22:29.163 [debug] QUERY OK db=0.0ms idle=6.9ms
PRAGMA foreign_key_list(comments) []

12:22:29.163 [debug] QUERY OK db=0.0ms idle=7.0ms
PRAGMA index_list(comments) []

12:22:29.163 [debug] QUERY OK db=0.0ms idle=7.0ms
PRAGMA index_info(comments_approved_index) []

12:22:29.163 [debug] QUERY OK db=0.0ms idle=7.1ms
PRAGMA index_info(comments_post_id_index) []

12:22:29.163 [debug] QUERY OK db=0.0ms idle=7.1ms
PRAGMA index_info(comments_user_id_index) []

12:22:29.163 [debug] QUERY OK db=0.0ms idle=7.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

12:22:29.164 [debug] QUERY OK db=0.0ms idle=7.6ms
PRAGMA table_info(comments) []

12:22:29.179 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

12:22:29.179 [debug] QUERY OK db=0.0ms idle=22.8ms
PRAGMA foreign_key_list(posts) []

12:22:29.179 [debug] QUERY OK db=0.0ms idle=22.8ms
PRAGMA index_list(posts) []

12:22:29.179 [debug] QUERY OK db=0.0ms idle=20.7ms
PRAGMA index_info(posts_title_index) []

12:22:29.179 [debug] QUERY OK db=0.0ms idle=16.1ms
PRAGMA index_info(posts_published_index) []

12:22:29.179 [debug] QUERY OK db=0.0ms idle=16.1ms
PRAGMA index_info(posts_user_id_index) []

12:22:29.179 [debug] QUERY OK db=0.0ms idle=16.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

12:22:29.179 [debug] QUERY OK db=0.0ms idle=16.1ms
PRAGMA table_info(posts) []

12:22:29.180 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

12:22:29.180 [debug] QUERY OK db=0.0ms idle=17.1ms
PRAGMA foreign_key_list(users) []

12:22:29.180 [debug] QUERY OK db=0.0ms idle=17.1ms
PRAGMA index_list(users) []

12:22:29.180 [debug] QUERY OK db=0.0ms idle=16.6ms
PRAGMA index_info(users_active_index) []

12:22:29.180 [debug] QUERY OK db=0.0ms idle=1.5ms
PRAGMA index_info(users_email_index) []

12:22:29.180 [debug] QUERY OK db=0.0ms idle=1.4ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

12:22:29.180 [debug] QUERY OK db=0.0ms idle=1.4ms
PRAGMA table_info(users) []

12:22:29.611 [info] Refreshing Drops.Relation cache...

12:22:29.617 [info] Repositories: [Sample.Repos.Sqlite]

12:22:29.618 [info] Tables: ["''"]

12:22:29.618 [info] Processing repository: Sample.Repos.Sqlite

12:22:29.623 [info]   Cache cleared for Sample.Repos.Sqlite

12:22:29.624 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.'' (not cached: {:file_read, :enoent})

12:22:29.629 [debug] QUERY OK db=0.4ms decode=0.4ms idle=7.4ms
PRAGMA foreign_key_list('') []

12:22:29.629 [debug] QUERY OK db=0.0ms idle=8.8ms
PRAGMA index_list('') []

12:22:29.629 [debug] QUERY OK db=0.0ms idle=8.9ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["''"]

12:22:29.629 [debug] QUERY OK db=0.0ms idle=9.0ms
PRAGMA table_info('') []

12:22:29.640 [info]   Cache warmed up for 1 tables

12:22:29.641 [info] Successful: 1

12:22:29.641 [info] Failed: 0

12:22:30.332 [debug] QUERY OK db=0.8ms decode=0.6ms queue=0.5ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

12:22:30.334 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

12:22:30.334 [debug] QUERY OK db=0.0ms idle=4.1ms
PRAGMA foreign_key_list(comments) []

12:22:30.334 [debug] QUERY OK db=0.0ms idle=4.2ms
PRAGMA index_list(comments) []

12:22:30.334 [debug] QUERY OK db=0.0ms idle=4.3ms
PRAGMA index_info(comments_approved_index) []

12:22:30.334 [debug] QUERY OK db=0.0ms idle=4.4ms
PRAGMA index_info(comments_post_id_index) []

12:22:30.334 [debug] QUERY OK db=0.0ms idle=4.4ms
PRAGMA index_info(comments_user_id_index) []

12:22:30.335 [debug] QUERY OK db=0.0ms idle=4.5ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

12:22:30.335 [debug] QUERY OK db=0.0ms idle=5.2ms
PRAGMA table_info(comments) []

12:22:30.380 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

12:22:30.381 [debug] QUERY OK db=0.0ms idle=51.0ms
PRAGMA foreign_key_list(posts) []

12:22:30.381 [debug] QUERY OK db=0.0ms idle=51.1ms
PRAGMA index_list(posts) []

12:22:30.381 [debug] QUERY OK db=0.0ms idle=50.4ms
PRAGMA index_info(posts_title_index) []

12:22:30.381 [debug] QUERY OK db=0.0ms idle=47.0ms
PRAGMA index_info(posts_published_index) []

12:22:30.381 [debug] QUERY OK db=0.0ms idle=47.0ms
PRAGMA index_info(posts_user_id_index) []

12:22:30.381 [debug] QUERY OK db=0.0ms idle=47.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

12:22:30.381 [debug] QUERY OK db=0.0ms idle=47.1ms
PRAGMA table_info(posts) []

12:22:30.395 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

12:22:30.395 [debug] QUERY OK db=0.0ms idle=60.8ms
PRAGMA foreign_key_list(users) []

12:22:30.395 [debug] QUERY OK db=0.0ms idle=60.8ms
PRAGMA index_list(users) []

12:22:30.395 [debug] QUERY OK db=0.0ms idle=60.1ms
PRAGMA index_info(users_active_index) []

12:22:30.395 [debug] QUERY OK db=0.0ms idle=14.4ms
PRAGMA index_info(users_email_index) []

12:22:30.395 [debug] QUERY OK db=0.0ms idle=14.4ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

12:22:30.395 [debug] QUERY OK db=0.0ms idle=14.5ms
PRAGMA table_info(users) []

12:22:32.131 [debug] QUERY OK db=0.5ms decode=0.5ms idle=0.6ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

12:22:32.716 [debug] QUERY OK db=0.5ms decode=0.4ms queue=1.2ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

12:22:33.311 [debug] QUERY OK db=0.5ms decode=0.6ms idle=36.2ms
PRAGMA foreign_key_list(users) []

12:22:33.312 [debug] QUERY OK db=0.0ms idle=39.4ms
PRAGMA index_list(users) []

12:22:33.312 [debug] QUERY OK db=0.0ms idle=39.6ms
PRAGMA index_info(users_active_index) []

12:22:33.313 [debug] QUERY OK db=0.0ms idle=39.7ms
PRAGMA index_info(users_email_index) []

12:22:33.313 [debug] QUERY OK db=0.0ms idle=39.8ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

12:22:33.313 [debug] QUERY OK db=0.0ms idle=40.6ms
PRAGMA table_info(users) []

12:22:33.789 [debug] QUERY OK db=0.6ms decode=0.5ms queue=1.2ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

12:22:34.473 [debug] QUERY OK db=0.5ms decode=0.5ms idle=31.2ms
PRAGMA foreign_key_list(users) []

12:22:34.474 [debug] QUERY OK db=0.0ms idle=34.3ms
PRAGMA index_list(users) []

12:22:34.474 [debug] QUERY OK db=0.0ms idle=34.4ms
PRAGMA index_info(users_active_index) []

12:22:34.474 [debug] QUERY OK db=0.0ms idle=34.6ms
PRAGMA index_info(users_email_index) []

12:22:34.475 [debug] QUERY OK db=0.0ms idle=34.6ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

12:22:34.475 [debug] QUERY OK db=0.0ms idle=35.3ms
PRAGMA table_info(users) []

12:24:02.580 [debug] QUERY OK db=0.5ms decode=0.5ms idle=2.4ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

12:24:02.582 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

12:24:02.582 [debug] QUERY OK db=0.0ms idle=7.5ms
PRAGMA foreign_key_list(comments) []

12:24:02.582 [debug] QUERY OK db=0.0ms idle=7.6ms
PRAGMA index_list(comments) []

12:24:02.582 [debug] QUERY OK db=0.0ms idle=7.6ms
PRAGMA index_info(comments_approved_index) []

12:24:02.582 [debug] QUERY OK db=0.0ms idle=7.4ms
PRAGMA index_info(comments_post_id_index) []

12:24:02.582 [debug] QUERY OK db=0.0ms idle=7.5ms
PRAGMA index_info(comments_user_id_index) []

12:24:02.583 [debug] QUERY OK db=0.0ms idle=7.5ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

12:24:02.583 [debug] QUERY OK db=0.0ms idle=8.0ms
PRAGMA table_info(comments) []

12:24:02.599 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

12:24:02.599 [debug] QUERY OK db=0.0ms idle=24.0ms
PRAGMA foreign_key_list(posts) []

12:24:02.599 [debug] QUERY OK db=0.0ms idle=24.0ms
PRAGMA index_list(posts) []

12:24:02.599 [debug] QUERY OK db=0.0ms idle=21.5ms
PRAGMA index_info(posts_title_index) []

12:24:02.599 [debug] QUERY OK db=0.0ms idle=17.0ms
PRAGMA index_info(posts_published_index) []

12:24:02.599 [debug] QUERY OK db=0.0ms idle=17.0ms
PRAGMA index_info(posts_user_id_index) []

12:24:02.599 [debug] QUERY OK db=0.0ms idle=16.9ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

12:24:02.599 [debug] QUERY OK db=0.0ms idle=16.9ms
PRAGMA table_info(posts) []

12:24:02.600 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

12:24:02.600 [debug] QUERY OK db=0.0ms idle=17.7ms
PRAGMA foreign_key_list(users) []

12:24:02.600 [debug] QUERY OK db=0.0ms idle=17.8ms
PRAGMA index_list(users) []

12:24:02.600 [debug] QUERY OK db=0.0ms idle=17.3ms
PRAGMA index_info(users_active_index) []

12:24:02.600 [debug] QUERY OK db=0.0ms idle=1.4ms
PRAGMA index_info(users_email_index) []

12:24:02.600 [debug] QUERY OK db=0.0ms idle=1.3ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

12:24:02.600 [debug] QUERY OK db=0.0ms idle=1.3ms
PRAGMA table_info(users) []

12:24:03.015 [info] Refreshing Drops.Relation cache...

12:24:03.020 [info] Repositories: [Sample.Repos.Sqlite]

12:24:03.021 [info] Tables: ["users", "posts"]

12:24:03.021 [info] Processing repository: Sample.Repos.Sqlite

12:24:03.027 [info]   Cache cleared for Sample.Repos.Sqlite

12:24:03.027 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

12:24:03.032 [debug] QUERY OK db=0.5ms decode=0.5ms idle=7.6ms
PRAGMA foreign_key_list(users) []

12:24:03.033 [debug] QUERY OK db=0.1ms idle=9.3ms
PRAGMA index_list(users) []

12:24:03.033 [debug] QUERY OK db=0.1ms idle=9.4ms
PRAGMA index_info(users_active_index) []

12:24:03.033 [debug] QUERY OK db=0.0ms idle=9.6ms
PRAGMA index_info(users_email_index) []

12:24:03.033 [debug] QUERY OK db=0.0ms idle=9.6ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

12:24:03.033 [debug] QUERY OK db=0.0ms idle=9.8ms
PRAGMA table_info(users) []

12:24:03.050 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

12:24:03.050 [debug] QUERY OK db=0.0ms idle=26.9ms
PRAGMA foreign_key_list(posts) []

12:24:03.050 [debug] QUERY OK db=0.0ms idle=27.0ms
PRAGMA index_list(posts) []

12:24:03.050 [debug] QUERY OK db=0.0ms idle=27.0ms
PRAGMA index_info(posts_title_index) []

12:24:03.050 [debug] QUERY OK db=0.0ms idle=26.8ms
PRAGMA index_info(posts_published_index) []

12:24:03.050 [debug] QUERY OK db=0.0ms idle=19.0ms
PRAGMA index_info(posts_user_id_index) []

12:24:03.051 [debug] QUERY OK db=0.0ms idle=17.8ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

12:24:03.051 [debug] QUERY OK db=0.0ms idle=17.8ms
PRAGMA table_info(posts) []

12:24:03.053 [info]   Cache warmed up for 2 tables

12:24:03.055 [info] Successful: 1

12:24:03.055 [info] Failed: 0

12:24:03.460 [info] Refreshing Drops.Relation cache...

12:24:03.467 [info] Repositories: [Sample.Repos.Sqlite]

12:24:03.469 [info] Tables: ["''"]

12:24:03.469 [info] Processing repository: Sample.Repos.Sqlite

12:24:03.475 [info]   Cache cleared for Sample.Repos.Sqlite

12:24:03.476 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.'' (not cached: {:file_read, :enoent})

12:24:03.482 [debug] QUERY OK db=0.6ms decode=0.7ms idle=8.7ms
PRAGMA foreign_key_list('') []

12:24:03.482 [debug] QUERY OK db=0.0ms idle=10.8ms
PRAGMA index_list('') []

12:24:03.482 [debug] QUERY OK db=0.1ms idle=10.9ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["''"]

12:24:03.482 [debug] QUERY OK db=0.0ms idle=11.0ms
PRAGMA table_info('') []

12:24:03.495 [info]   Cache warmed up for 1 tables

12:24:03.496 [info] Successful: 1

12:24:03.496 [info] Failed: 0

12:24:04.118 [info] Refreshing Drops.Relation cache...

12:24:04.120 [info] Repositories: [Sample.Repos.Sqlite]

12:24:04.122 [info] Tables: ["non_existent_table"]

12:24:04.122 [info] Processing repository: Sample.Repos.Sqlite

12:24:04.124 [info]   Cache cleared for Sample.Repos.Sqlite

12:24:04.125 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.non_existent_table (not cached: {:file_read, :enoent})

12:24:04.132 [debug] QUERY OK db=0.7ms decode=0.6ms idle=3.0ms
PRAGMA foreign_key_list(non_existent_table) []

12:24:04.132 [debug] QUERY OK db=0.0ms idle=5.1ms
PRAGMA index_list(non_existent_table) []

12:24:04.133 [debug] QUERY OK db=0.1ms idle=5.3ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["non_existent_table"]

12:24:04.133 [debug] QUERY OK db=0.0ms idle=5.4ms
PRAGMA table_info(non_existent_table) []

12:24:04.144 [info]   Cache warmed up for 1 tables

12:24:04.144 [info] Successful: 1

12:24:04.144 [info] Failed: 0

12:24:04.560 [info] Refreshing Drops.Relation cache...

12:24:04.568 [info] Repositories: [Sample.Repos.Sqlite]

12:24:04.568 [info] Tables: all

12:24:04.569 [info] Processing repository: Sample.Repos.Sqlite

12:24:04.575 [info]   Cache cleared for Sample.Repos.Sqlite

12:24:04.581 [debug] QUERY OK db=0.7ms decode=0.6ms idle=5.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

12:24:04.582 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

12:24:04.582 [debug] QUERY OK db=0.1ms idle=8.2ms
PRAGMA foreign_key_list(comments) []

12:24:04.583 [debug] QUERY OK db=0.1ms idle=8.4ms
PRAGMA index_list(comments) []

12:24:04.583 [debug] QUERY OK db=0.0ms idle=8.6ms
PRAGMA index_info(comments_approved_index) []

12:24:04.583 [debug] QUERY OK db=0.0ms idle=8.7ms
PRAGMA index_info(comments_post_id_index) []

12:24:04.583 [debug] QUERY OK db=0.0ms idle=8.7ms
PRAGMA index_info(comments_user_id_index) []

12:24:04.583 [debug] QUERY OK db=0.0ms idle=8.8ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

12:24:04.584 [debug] QUERY OK db=0.0ms idle=9.6ms
PRAGMA table_info(comments) []

12:24:04.601 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

12:24:04.601 [debug] QUERY OK db=0.0ms idle=27.0ms
PRAGMA foreign_key_list(posts) []

12:24:04.601 [debug] QUERY OK db=0.1ms idle=27.1ms
PRAGMA index_list(posts) []

12:24:04.601 [debug] QUERY OK db=0.0ms idle=21.5ms
PRAGMA index_info(posts_title_index) []

12:24:04.602 [debug] QUERY OK db=0.0ms idle=19.0ms
PRAGMA index_info(posts_published_index) []

12:24:04.602 [debug] QUERY OK db=0.0ms idle=18.9ms
PRAGMA index_info(posts_user_id_index) []

12:24:04.602 [debug] QUERY OK db=0.0ms idle=19.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

12:24:04.602 [debug] QUERY OK db=0.0ms idle=18.9ms
PRAGMA table_info(posts) []

12:24:04.603 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

12:24:04.603 [debug] QUERY OK db=0.0ms idle=19.8ms
PRAGMA foreign_key_list(users) []

12:24:04.603 [debug] QUERY OK db=0.1ms idle=19.8ms
PRAGMA index_list(users) []

12:24:04.603 [debug] QUERY OK db=0.0ms idle=19.1ms
PRAGMA index_info(users_active_index) []

12:24:04.603 [debug] QUERY OK db=0.0ms idle=1.8ms
PRAGMA index_info(users_email_index) []

12:24:04.603 [debug] QUERY OK db=0.0ms idle=1.7ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

12:24:04.603 [debug] QUERY OK db=0.0ms idle=1.7ms
PRAGMA table_info(users) []

12:24:04.605 [info]   Cache warmed up for 3 tables

12:24:04.606 [info] Successful: 1

12:24:04.606 [info] Failed: 0

12:24:05.233 [info] Refreshing Drops.Relation cache...

12:24:05.236 [info] Repositories: [Sample.Repos.Sqlite]

12:24:05.236 [info] Tables: all

12:24:05.237 [info] Processing repository: Sample.Repos.Sqlite

12:24:05.240 [info]   Cache cleared for Sample.Repos.Sqlite

12:24:05.245 [debug] QUERY OK db=0.5ms decode=0.5ms idle=3.9ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

12:24:05.246 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

12:24:05.246 [debug] QUERY OK db=0.0ms idle=6.2ms
PRAGMA foreign_key_list(comments) []

12:24:05.246 [debug] QUERY OK db=0.0ms idle=6.3ms
PRAGMA index_list(comments) []

12:24:05.246 [debug] QUERY OK db=0.0ms idle=6.3ms
PRAGMA index_info(comments_approved_index) []

12:24:05.246 [debug] QUERY OK db=0.1ms idle=6.1ms
PRAGMA index_info(comments_post_id_index) []

12:24:05.246 [debug] QUERY OK db=0.0ms idle=6.3ms
PRAGMA index_info(comments_user_id_index) []

12:24:05.247 [debug] QUERY OK db=0.0ms idle=6.3ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

12:24:05.247 [debug] QUERY OK db=0.0ms idle=7.0ms
PRAGMA table_info(comments) []

12:24:05.262 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

12:24:05.263 [debug] QUERY OK db=0.1ms idle=22.4ms
PRAGMA foreign_key_list(posts) []

12:24:05.263 [debug] QUERY OK db=0.1ms idle=22.5ms
PRAGMA index_list(posts) []

12:24:05.263 [debug] QUERY OK db=0.1ms idle=18.6ms
PRAGMA index_info(posts_title_index) []

12:24:05.263 [debug] QUERY OK db=0.0ms idle=16.9ms
PRAGMA index_info(posts_published_index) []

12:24:05.263 [debug] QUERY OK db=0.0ms idle=16.9ms
PRAGMA index_info(posts_user_id_index) []

12:24:05.263 [debug] QUERY OK db=0.1ms idle=16.9ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

12:24:05.263 [debug] QUERY OK db=0.1ms idle=16.9ms
PRAGMA table_info(posts) []

12:24:05.264 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

12:24:05.265 [debug] QUERY OK db=0.1ms idle=17.9ms
PRAGMA foreign_key_list(users) []

12:24:05.265 [debug] QUERY OK db=0.0ms idle=18.0ms
PRAGMA index_list(users) []

12:24:05.265 [debug] QUERY OK db=0.0ms idle=17.5ms
PRAGMA index_info(users_active_index) []

12:24:05.265 [debug] QUERY OK db=0.0ms idle=2.0ms
PRAGMA index_info(users_email_index) []

12:24:05.265 [debug] QUERY OK db=0.0ms idle=1.9ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

12:24:05.265 [debug] QUERY OK db=0.0ms idle=1.9ms
PRAGMA table_info(users) []

12:24:05.266 [info]   Cache warmed up for 3 tables

12:24:05.267 [info] Successful: 1

12:24:05.267 [info] Failed: 0

12:24:06.252 [info] Refreshing Drops.Relation cache...

12:24:06.256 [info] Repositories: [Sample.Repos.Sqlite]

12:24:06.256 [info] Tables: all

12:24:06.256 [info] Processing repository: Sample.Repos.Sqlite

12:24:06.257 [info]   Cache cleared for Sample.Repos.Sqlite

12:24:06.264 [debug] QUERY OK db=0.6ms decode=0.6ms idle=2.3ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

12:24:06.265 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

12:24:06.265 [debug] QUERY OK db=0.0ms idle=5.0ms
PRAGMA foreign_key_list(comments) []

12:24:06.265 [debug] QUERY OK db=0.0ms idle=5.1ms
PRAGMA index_list(comments) []

12:24:06.265 [debug] QUERY OK db=0.0ms idle=5.2ms
PRAGMA index_info(comments_approved_index) []

12:24:06.265 [debug] QUERY OK db=0.0ms idle=5.2ms
PRAGMA index_info(comments_post_id_index) []

12:24:06.265 [debug] QUERY OK db=0.0ms idle=5.3ms
PRAGMA index_info(comments_user_id_index) []

12:24:06.266 [debug] QUERY OK db=0.0ms idle=5.3ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

12:24:06.266 [debug] QUERY OK db=0.0ms idle=5.9ms
PRAGMA table_info(comments) []

12:24:06.278 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

12:24:06.278 [debug] QUERY OK db=0.0ms idle=17.7ms
PRAGMA foreign_key_list(posts) []

12:24:06.278 [debug] QUERY OK db=0.0ms idle=17.7ms
PRAGMA index_list(posts) []

12:24:06.278 [debug] QUERY OK db=0.0ms idle=14.8ms
PRAGMA index_info(posts_title_index) []

12:24:06.278 [debug] QUERY OK db=0.0ms idle=12.7ms
PRAGMA index_info(posts_published_index) []

12:24:06.278 [debug] QUERY OK db=0.0ms idle=12.8ms
PRAGMA index_info(posts_user_id_index) []

12:24:06.278 [debug] QUERY OK db=0.0ms idle=12.8ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

12:24:06.278 [debug] QUERY OK db=0.0ms idle=12.8ms
PRAGMA table_info(posts) []

12:24:06.279 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

12:24:06.279 [debug] QUERY OK db=0.0ms idle=13.6ms
PRAGMA foreign_key_list(users) []

12:24:06.279 [debug] QUERY OK db=0.0ms idle=13.6ms
PRAGMA index_list(users) []

12:24:06.279 [debug] QUERY OK db=0.0ms idle=13.1ms
PRAGMA index_info(users_active_index) []

12:24:06.279 [debug] QUERY OK db=0.0ms idle=1.5ms
PRAGMA index_info(users_email_index) []

12:24:06.279 [debug] QUERY OK db=0.0ms idle=1.4ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

12:24:06.280 [debug] QUERY OK db=0.0ms idle=1.5ms
PRAGMA table_info(users) []

12:24:06.281 [info]   Cache warmed up for 3 tables

12:24:06.281 [info] Successful: 1

12:24:06.281 [info] Failed: 0

12:24:06.717 [info] Refreshing Drops.Relation cache...

12:24:06.722 [info] Repositories: [Sample.Repos.Sqlite]

12:24:06.722 [info] Tables: all

12:24:06.723 [info] Processing repository: Sample.Repos.Sqlite

12:24:06.729 [info]   Cache cleared for Sample.Repos.Sqlite

12:24:06.735 [debug] QUERY OK db=0.7ms decode=0.6ms idle=5.2ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

12:24:06.735 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

12:24:06.736 [debug] QUERY OK db=0.1ms idle=7.9ms
PRAGMA foreign_key_list(comments) []

12:24:06.736 [debug] QUERY OK db=0.0ms idle=8.1ms
PRAGMA index_list(comments) []

12:24:06.736 [debug] QUERY OK db=0.0ms idle=8.2ms
PRAGMA index_info(comments_approved_index) []

12:24:06.736 [debug] QUERY OK db=0.0ms idle=8.2ms
PRAGMA index_info(comments_post_id_index) []

12:24:06.736 [debug] QUERY OK db=0.0ms idle=8.3ms
PRAGMA index_info(comments_user_id_index) []

12:24:06.736 [debug] QUERY OK db=0.0ms idle=8.4ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

12:24:06.737 [debug] QUERY OK db=0.0ms idle=8.9ms
PRAGMA table_info(comments) []

12:24:06.752 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

12:24:06.752 [debug] QUERY OK db=0.0ms idle=24.6ms
PRAGMA foreign_key_list(posts) []

12:24:06.752 [debug] QUERY OK db=0.0ms idle=24.6ms
PRAGMA index_list(posts) []

12:24:06.752 [debug] QUERY OK db=0.0ms idle=18.8ms
PRAGMA index_info(posts_title_index) []

12:24:06.752 [debug] QUERY OK db=0.0ms idle=16.7ms
PRAGMA index_info(posts_published_index) []

12:24:06.752 [debug] QUERY OK db=0.0ms idle=16.6ms
PRAGMA index_info(posts_user_id_index) []

12:24:06.752 [debug] QUERY OK db=0.0ms idle=16.6ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

12:24:06.753 [debug] QUERY OK db=0.0ms idle=16.6ms
PRAGMA table_info(posts) []

12:24:06.753 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

12:24:06.753 [debug] QUERY OK db=0.0ms idle=17.3ms
PRAGMA foreign_key_list(users) []

12:24:06.753 [debug] QUERY OK db=0.0ms idle=17.4ms
PRAGMA index_list(users) []

12:24:06.754 [debug] QUERY OK db=0.0ms idle=16.9ms
PRAGMA index_info(users_active_index) []

12:24:06.754 [debug] QUERY OK db=0.0ms idle=1.3ms
PRAGMA index_info(users_email_index) []

12:24:06.754 [debug] QUERY OK db=0.0ms idle=1.2ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

12:24:06.754 [debug] QUERY OK db=0.0ms idle=1.2ms
PRAGMA table_info(users) []

12:24:06.755 [info]   Cache warmed up for 3 tables

12:24:06.756 [info] Successful: 1

12:24:06.756 [info] Failed: 0

12:24:07.475 [debug] QUERY OK db=0.6ms decode=0.5ms queue=1.1ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

12:24:08.057 [debug] QUERY OK db=0.6ms decode=0.5ms queue=0.1ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

12:24:08.739 [debug] QUERY OK db=0.5ms decode=0.5ms idle=36.6ms
PRAGMA foreign_key_list(users) []

12:24:08.740 [debug] QUERY OK db=0.0ms idle=39.9ms
PRAGMA index_list(users) []

12:24:08.740 [debug] QUERY OK db=0.0ms idle=40.0ms
PRAGMA index_info(users_active_index) []

12:24:08.740 [debug] QUERY OK db=0.0ms idle=40.0ms
PRAGMA index_info(users_email_index) []

12:24:08.741 [debug] QUERY OK db=0.0ms idle=40.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

12:24:08.741 [debug] QUERY OK db=0.1ms idle=41.0ms
PRAGMA table_info(users) []

12:24:09.234 [debug] QUERY OK db=0.7ms decode=0.4ms queue=1.0ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

12:24:10.368 [debug] QUERY OK db=0.6ms decode=0.5ms idle=31.2ms
PRAGMA foreign_key_list(users) []

12:24:10.369 [debug] QUERY OK db=0.1ms idle=34.9ms
PRAGMA index_list(users) []

12:24:10.369 [debug] QUERY OK db=0.0ms idle=35.1ms
PRAGMA index_info(users_active_index) []

12:24:10.369 [debug] QUERY OK db=0.0ms idle=35.1ms
PRAGMA index_info(users_email_index) []

12:24:10.370 [debug] QUERY OK db=0.0ms idle=35.2ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

12:24:10.370 [debug] QUERY OK db=0.0ms idle=36.0ms
PRAGMA table_info(users) []

12:24:10.826 [debug] QUERY OK db=0.9ms decode=0.7ms queue=1.0ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

13:07:40.912 [debug] QUERY OK db=0.7ms decode=0.6ms queue=0.8ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

13:07:41.464 [debug] QUERY OK db=0.7ms decode=0.6ms queue=1.0ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

13:07:42.087 [debug] QUERY OK db=0.5ms decode=0.6ms idle=29.6ms
PRAGMA foreign_key_list(users) []

13:07:42.088 [debug] QUERY OK db=0.0ms idle=32.9ms
PRAGMA index_list(users) []

13:07:42.088 [debug] QUERY OK db=0.0ms idle=33.0ms
PRAGMA index_info(users_active_index) []

13:07:42.088 [debug] QUERY OK db=0.0ms idle=33.1ms
PRAGMA index_info(users_email_index) []

13:07:42.089 [debug] QUERY OK db=0.0ms idle=33.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

13:07:42.089 [debug] QUERY OK db=0.0ms idle=33.7ms
PRAGMA table_info(users) []

13:07:42.592 [debug] QUERY OK db=0.7ms decode=0.5ms idle=42.0ms
PRAGMA foreign_key_list(users) []

13:07:42.593 [debug] QUERY OK db=0.0ms idle=45.4ms
PRAGMA index_list(users) []

13:07:42.593 [debug] QUERY OK db=0.0ms idle=45.6ms
PRAGMA index_info(users_active_index) []

13:07:42.593 [debug] QUERY OK db=0.0ms idle=45.7ms
PRAGMA index_info(users_email_index) []

13:07:42.594 [debug] QUERY OK db=0.0ms idle=45.8ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

13:07:42.594 [debug] QUERY OK db=0.1ms idle=46.7ms
PRAGMA table_info(users) []

13:07:43.085 [debug] QUERY OK db=0.6ms decode=0.4ms queue=1.0ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

13:07:44.138 [debug] QUERY OK db=0.5ms decode=0.5ms queue=1.1ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

13:07:45.024 [info] Refreshing Drops.Relation cache...

13:07:45.030 [info] Repositories: [Sample.Repos.Sqlite]

13:07:45.030 [info] Tables: all

13:07:45.030 [info] Processing repository: Sample.Repos.Sqlite

13:07:45.037 [info]   Cache cleared for Sample.Repos.Sqlite

13:07:45.042 [debug] QUERY OK db=0.6ms decode=0.4ms idle=4.9ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

13:07:45.043 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

13:07:45.043 [debug] QUERY OK db=0.0ms idle=7.1ms
PRAGMA foreign_key_list(comments) []

13:07:45.043 [debug] QUERY OK db=0.0ms idle=7.2ms
PRAGMA index_list(comments) []

13:07:45.043 [debug] QUERY OK db=0.0ms idle=7.3ms
PRAGMA index_info(comments_approved_index) []

13:07:45.043 [debug] QUERY OK db=0.0ms idle=7.3ms
PRAGMA index_info(comments_post_id_index) []

13:07:45.043 [debug] QUERY OK db=0.0ms idle=7.4ms
PRAGMA index_info(comments_user_id_index) []

13:07:45.044 [debug] QUERY OK db=0.0ms idle=7.2ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

13:07:45.044 [debug] QUERY OK db=0.0ms idle=7.8ms
PRAGMA table_info(comments) []

13:07:45.060 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

13:07:45.060 [debug] QUERY OK db=0.0ms idle=23.8ms
PRAGMA foreign_key_list(posts) []

13:07:45.060 [debug] QUERY OK db=0.0ms idle=23.8ms
PRAGMA index_list(posts) []

13:07:45.060 [debug] QUERY OK db=0.0ms idle=18.7ms
PRAGMA index_info(posts_title_index) []

13:07:45.060 [debug] QUERY OK db=0.0ms idle=17.0ms
PRAGMA index_info(posts_published_index) []

13:07:45.060 [debug] QUERY OK db=0.0ms idle=16.9ms
PRAGMA index_info(posts_user_id_index) []

13:07:45.060 [debug] QUERY OK db=0.0ms idle=16.9ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

13:07:45.060 [debug] QUERY OK db=0.0ms idle=16.9ms
PRAGMA table_info(posts) []

13:07:45.061 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

13:07:45.061 [debug] QUERY OK db=0.0ms idle=17.5ms
PRAGMA foreign_key_list(users) []

13:07:45.061 [debug] QUERY OK db=0.0ms idle=17.4ms
PRAGMA index_list(users) []

13:07:45.061 [debug] QUERY OK db=0.0ms idle=16.9ms
PRAGMA index_info(users_active_index) []

13:07:45.061 [debug] QUERY OK db=0.0ms idle=0.9ms
PRAGMA index_info(users_email_index) []

13:07:45.061 [debug] QUERY OK db=0.0ms idle=0.9ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

13:07:45.061 [debug] QUERY OK db=0.0ms idle=0.9ms
PRAGMA table_info(users) []

13:07:45.062 [info]   Cache warmed up for 3 tables

13:07:45.063 [info] Successful: 1

13:07:45.063 [info] Failed: 0

13:07:45.840 [info] Refreshing Drops.Relation cache...

13:07:45.846 [info] Repositories: [Sample.Repos.Sqlite]

13:07:45.848 [info] Tables: ["users", "posts"]

13:07:45.848 [info] Processing repository: Sample.Repos.Sqlite

13:07:45.854 [info]   Cache cleared for Sample.Repos.Sqlite

13:07:45.855 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

13:07:45.860 [debug] QUERY OK db=0.5ms decode=0.5ms idle=5.3ms
PRAGMA foreign_key_list(users) []

13:07:45.860 [debug] QUERY OK db=0.0ms idle=6.9ms
PRAGMA index_list(users) []

13:07:45.860 [debug] QUERY OK db=0.0ms idle=6.9ms
PRAGMA index_info(users_active_index) []

13:07:45.860 [debug] QUERY OK db=0.0ms idle=7.1ms
PRAGMA index_info(users_email_index) []

13:07:45.860 [debug] QUERY OK db=0.0ms idle=7.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

13:07:45.860 [debug] QUERY OK db=0.0ms idle=7.2ms
PRAGMA table_info(users) []

13:07:45.873 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

13:07:45.874 [debug] QUERY OK db=0.0ms idle=20.5ms
PRAGMA foreign_key_list(posts) []

13:07:45.874 [debug] QUERY OK db=0.0ms idle=20.5ms
PRAGMA index_list(posts) []

13:07:45.874 [debug] QUERY OK db=0.0ms idle=20.6ms
PRAGMA index_info(posts_title_index) []

13:07:45.874 [debug] QUERY OK db=0.0ms idle=20.7ms
PRAGMA index_info(posts_published_index) []

13:07:45.874 [debug] QUERY OK db=0.0ms idle=14.9ms
PRAGMA index_info(posts_user_id_index) []

13:07:45.874 [debug] QUERY OK db=0.0ms idle=13.8ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

13:07:45.874 [debug] QUERY OK db=0.0ms idle=13.8ms
PRAGMA table_info(posts) []

13:07:45.876 [info]   Cache warmed up for 2 tables

13:07:45.877 [info] Successful: 1

13:07:45.877 [info] Failed: 0

13:07:46.288 [info] Refreshing Drops.Relation cache...

13:07:46.294 [info] Repositories: [Sample.Repos.Sqlite]

13:07:46.294 [info] Tables: all

13:07:46.294 [info] Processing repository: Sample.Repos.Sqlite

13:07:46.300 [info]   Cache cleared for Sample.Repos.Sqlite

13:07:46.304 [debug] QUERY OK db=0.5ms decode=0.4ms idle=5.2ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

13:07:46.305 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

13:07:46.305 [debug] QUERY OK db=0.0ms idle=7.4ms
PRAGMA foreign_key_list(comments) []

13:07:46.305 [debug] QUERY OK db=0.0ms idle=7.5ms
PRAGMA index_list(comments) []

13:07:46.305 [debug] QUERY OK db=0.0ms idle=7.5ms
PRAGMA index_info(comments_approved_index) []

13:07:46.305 [debug] QUERY OK db=0.0ms idle=7.5ms
PRAGMA index_info(comments_post_id_index) []

13:07:46.305 [debug] QUERY OK db=0.0ms idle=7.6ms
PRAGMA index_info(comments_user_id_index) []

13:07:46.306 [debug] QUERY OK db=0.0ms idle=7.6ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

13:07:46.306 [debug] QUERY OK db=0.0ms idle=8.2ms
PRAGMA table_info(comments) []

13:07:46.322 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

13:07:46.322 [debug] QUERY OK db=0.0ms idle=24.3ms
PRAGMA foreign_key_list(posts) []

13:07:46.322 [debug] QUERY OK db=0.0ms idle=24.3ms
PRAGMA index_list(posts) []

13:07:46.322 [debug] QUERY OK db=0.0ms idle=18.6ms
PRAGMA index_info(posts_title_index) []

13:07:46.322 [debug] QUERY OK db=0.0ms idle=17.0ms
PRAGMA index_info(posts_published_index) []

13:07:46.322 [debug] QUERY OK db=0.0ms idle=17.0ms
PRAGMA index_info(posts_user_id_index) []

13:07:46.322 [debug] QUERY OK db=0.0ms idle=17.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

13:07:46.322 [debug] QUERY OK db=0.0ms idle=17.0ms
PRAGMA table_info(posts) []

13:07:46.323 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

13:07:46.323 [debug] QUERY OK db=0.0ms idle=17.8ms
PRAGMA foreign_key_list(users) []

13:07:46.323 [debug] QUERY OK db=0.0ms idle=17.8ms
PRAGMA index_list(users) []

13:07:46.323 [debug] QUERY OK db=0.0ms idle=17.2ms
PRAGMA index_info(users_active_index) []

13:07:46.323 [debug] QUERY OK db=0.0ms idle=1.2ms
PRAGMA index_info(users_email_index) []

13:07:46.323 [debug] QUERY OK db=0.0ms idle=1.2ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

13:07:46.323 [debug] QUERY OK db=0.0ms idle=1.2ms
PRAGMA table_info(users) []

13:07:46.325 [info]   Cache warmed up for 3 tables

13:07:46.326 [info] Successful: 1

13:07:46.326 [info] Failed: 0

13:07:46.744 [info] Refreshing Drops.Relation cache...

13:07:46.750 [info] Repositories: [Sample.Repos.Sqlite]

13:07:46.750 [info] Tables: all

13:07:46.751 [info] Processing repository: Sample.Repos.Sqlite

13:07:46.757 [info]   Cache cleared for Sample.Repos.Sqlite

13:07:46.762 [debug] QUERY OK db=0.5ms decode=0.5ms idle=5.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

13:07:46.763 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

13:07:46.763 [debug] QUERY OK db=0.0ms idle=7.3ms
PRAGMA foreign_key_list(comments) []

13:07:46.763 [debug] QUERY OK db=0.0ms idle=7.4ms
PRAGMA index_list(comments) []

13:07:46.763 [debug] QUERY OK db=0.0ms idle=7.4ms
PRAGMA index_info(comments_approved_index) []

13:07:46.763 [debug] QUERY OK db=0.0ms idle=7.5ms
PRAGMA index_info(comments_post_id_index) []

13:07:46.763 [debug] QUERY OK db=0.0ms idle=7.5ms
PRAGMA index_info(comments_user_id_index) []

13:07:46.764 [debug] QUERY OK db=0.0ms idle=7.6ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

13:07:46.764 [debug] QUERY OK db=0.0ms idle=8.1ms
PRAGMA table_info(comments) []

13:07:46.786 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

13:07:46.786 [debug] QUERY OK db=0.0ms idle=30.0ms
PRAGMA foreign_key_list(posts) []

13:07:46.786 [debug] QUERY OK db=0.0ms idle=30.2ms
PRAGMA index_list(posts) []

13:07:46.786 [debug] QUERY OK db=0.0ms idle=24.7ms
PRAGMA index_info(posts_title_index) []

13:07:46.786 [debug] QUERY OK db=0.1ms idle=23.0ms
PRAGMA index_info(posts_published_index) []

13:07:46.786 [debug] QUERY OK db=0.0ms idle=23.1ms
PRAGMA index_info(posts_user_id_index) []

13:07:46.787 [debug] QUERY OK db=0.1ms idle=23.2ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

13:07:46.787 [debug] QUERY OK db=0.1ms idle=23.3ms
PRAGMA table_info(posts) []

13:07:46.788 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

13:07:46.788 [debug] QUERY OK db=0.0ms idle=24.5ms
PRAGMA foreign_key_list(users) []

13:07:46.788 [debug] QUERY OK db=0.0ms idle=24.5ms
PRAGMA index_list(users) []

13:07:46.788 [debug] QUERY OK db=0.0ms idle=24.1ms
PRAGMA index_info(users_active_index) []

13:07:46.788 [debug] QUERY OK db=0.0ms idle=2.2ms
PRAGMA index_info(users_email_index) []

13:07:46.788 [debug] QUERY OK db=0.1ms idle=2.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

13:07:46.788 [debug] QUERY OK db=0.0ms idle=2.1ms
PRAGMA table_info(users) []

13:07:46.790 [info]   Cache warmed up for 3 tables

13:07:46.791 [info] Successful: 1

13:07:46.791 [info] Failed: 0

13:07:47.217 [info] Refreshing Drops.Relation cache...

13:07:47.223 [info] Repositories: [Sample.Repos.Sqlite]

13:07:47.225 [info] Tables: ["''"]

13:07:47.225 [info] Processing repository: Sample.Repos.Sqlite

13:07:47.230 [info]   Cache cleared for Sample.Repos.Sqlite

13:07:47.233 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.'' (not cached: {:file_read, :enoent})

13:07:47.238 [debug] QUERY OK db=0.5ms decode=0.5ms idle=10.9ms
PRAGMA foreign_key_list('') []

13:07:47.238 [debug] QUERY OK db=0.0ms idle=12.6ms
PRAGMA index_list('') []

13:07:47.238 [debug] QUERY OK db=0.0ms idle=12.7ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["''"]

13:07:47.239 [debug] QUERY OK db=0.0ms idle=12.8ms
PRAGMA table_info('') []

13:07:47.251 [info]   Cache warmed up for 1 tables

13:07:47.253 [info] Successful: 1

13:07:47.253 [info] Failed: 0

13:07:47.930 [debug] QUERY OK db=0.7ms decode=0.5ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

13:07:47.931 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

13:07:47.931 [debug] QUERY OK db=0.0ms idle=3.6ms
PRAGMA foreign_key_list(comments) []

13:07:47.931 [debug] QUERY OK db=0.0ms idle=3.7ms
PRAGMA index_list(comments) []

13:07:47.931 [debug] QUERY OK db=0.0ms idle=3.8ms
PRAGMA index_info(comments_approved_index) []

13:07:47.931 [debug] QUERY OK db=0.0ms idle=3.8ms
PRAGMA index_info(comments_post_id_index) []

13:07:47.931 [debug] QUERY OK db=0.0ms idle=3.9ms
PRAGMA index_info(comments_user_id_index) []

13:07:47.932 [debug] QUERY OK db=0.0ms idle=3.9ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

13:07:47.932 [debug] QUERY OK db=0.0ms idle=4.6ms
PRAGMA table_info(comments) []

13:07:47.950 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

13:07:47.950 [debug] QUERY OK db=0.0ms idle=22.7ms
PRAGMA foreign_key_list(posts) []

13:07:47.950 [debug] QUERY OK db=0.1ms idle=22.9ms
PRAGMA index_list(posts) []

13:07:47.950 [debug] QUERY OK db=0.0ms idle=22.3ms
PRAGMA index_info(posts_title_index) []

13:07:47.951 [debug] QUERY OK db=0.0ms idle=19.5ms
PRAGMA index_info(posts_published_index) []

13:07:47.951 [debug] QUERY OK db=0.0ms idle=19.4ms
PRAGMA index_info(posts_user_id_index) []

13:07:47.951 [debug] QUERY OK db=0.1ms idle=19.4ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

13:07:47.951 [debug] QUERY OK db=0.0ms idle=19.5ms
PRAGMA table_info(posts) []

13:07:47.952 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

13:07:47.952 [debug] QUERY OK db=0.0ms idle=20.9ms
PRAGMA foreign_key_list(users) []

13:07:47.952 [debug] QUERY OK db=0.0ms idle=20.9ms
PRAGMA index_list(users) []

13:07:47.952 [debug] QUERY OK db=0.1ms idle=20.3ms
PRAGMA index_info(users_active_index) []

13:07:47.953 [debug] QUERY OK db=0.1ms idle=2.4ms
PRAGMA index_info(users_email_index) []

13:07:47.953 [debug] QUERY OK db=0.0ms idle=2.3ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

13:07:47.953 [debug] QUERY OK db=0.0ms idle=2.3ms
PRAGMA table_info(users) []

13:11:44.617 [info] Refreshing Drops.Relation cache...

13:11:44.620 [info] Repositories: [Sample.Repos.Sqlite]

13:11:44.620 [info] Tables: all

13:11:44.620 [info] Processing repository: Sample.Repos.Sqlite

13:11:44.621 [info]   Cache cleared for Sample.Repos.Sqlite

13:11:44.633 [debug] QUERY OK db=0.7ms decode=0.6ms idle=0.8ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

13:11:44.633 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

13:11:44.634 [debug] QUERY OK db=0.0ms idle=3.7ms
PRAGMA foreign_key_list(comments) []

13:11:44.634 [debug] QUERY OK db=0.0ms idle=3.8ms
PRAGMA index_list(comments) []

13:11:44.634 [debug] QUERY OK db=0.0ms idle=3.9ms
PRAGMA index_info(comments_approved_index) []

13:11:44.634 [debug] QUERY OK db=0.0ms idle=3.9ms
PRAGMA index_info(comments_post_id_index) []

13:11:44.634 [debug] QUERY OK db=0.0ms idle=4.0ms
PRAGMA index_info(comments_user_id_index) []

13:11:44.635 [debug] QUERY OK db=0.0ms idle=4.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

13:11:44.635 [debug] QUERY OK db=0.0ms idle=4.8ms
PRAGMA table_info(comments) []

13:11:44.650 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

13:11:44.650 [debug] QUERY OK db=0.0ms idle=20.3ms
PRAGMA foreign_key_list(posts) []

13:11:44.650 [debug] QUERY OK db=0.0ms idle=20.3ms
PRAGMA index_list(posts) []

13:11:44.650 [debug] QUERY OK db=0.0ms idle=18.9ms
PRAGMA index_info(posts_title_index) []

13:11:44.650 [debug] QUERY OK db=0.0ms idle=16.7ms
PRAGMA index_info(posts_published_index) []

13:11:44.650 [debug] QUERY OK db=0.0ms idle=16.6ms
PRAGMA index_info(posts_user_id_index) []

13:11:44.650 [debug] QUERY OK db=0.0ms idle=16.6ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

13:11:44.650 [debug] QUERY OK db=0.0ms idle=16.6ms
PRAGMA table_info(posts) []

13:11:44.651 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

13:11:44.651 [debug] QUERY OK db=0.0ms idle=17.3ms
PRAGMA foreign_key_list(users) []

13:11:44.651 [debug] QUERY OK db=0.0ms idle=17.3ms
PRAGMA index_list(users) []

13:11:44.651 [debug] QUERY OK db=0.0ms idle=16.6ms
PRAGMA index_info(users_active_index) []

13:11:44.651 [debug] QUERY OK db=0.0ms idle=1.2ms
PRAGMA index_info(users_email_index) []

13:11:44.652 [debug] QUERY OK db=0.0ms idle=1.2ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

13:11:44.652 [debug] QUERY OK db=0.0ms idle=1.2ms
PRAGMA table_info(users) []

13:11:44.653 [info]   Cache warmed up for 3 tables

13:11:44.653 [info] Successful: 1

13:11:44.653 [info] Failed: 0

13:11:45.261 [info] Refreshing Drops.Relation cache...

13:11:45.264 [info] Repositories: [Sample.Repos.Sqlite]

13:11:45.266 [info] Tables: ["non_existent_table"]

13:11:45.266 [info] Processing repository: Sample.Repos.Sqlite

13:11:45.267 [info]   Cache cleared for Sample.Repos.Sqlite

13:11:45.268 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.non_existent_table (not cached: {:file_read, :enoent})

13:11:45.275 [debug] QUERY OK db=0.8ms decode=0.7ms idle=2.9ms
PRAGMA foreign_key_list(non_existent_table) []

13:11:45.275 [debug] QUERY OK db=0.0ms idle=5.1ms
PRAGMA index_list(non_existent_table) []

13:11:45.275 [debug] QUERY OK db=0.1ms idle=5.2ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["non_existent_table"]

13:11:45.275 [debug] QUERY OK db=0.0ms idle=5.4ms
PRAGMA table_info(non_existent_table) []

13:11:45.283 [info]   Cache warmed up for 1 tables

13:11:45.283 [info] Successful: 1

13:11:45.283 [info] Failed: 0

13:11:45.896 [debug] QUERY OK db=0.6ms decode=0.5ms queue=0.5ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

13:11:45.896 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

13:11:45.896 [debug] QUERY OK db=0.0ms idle=2.9ms
PRAGMA foreign_key_list(comments) []

13:11:45.896 [debug] QUERY OK db=0.0ms idle=3.0ms
PRAGMA index_list(comments) []

13:11:45.896 [debug] QUERY OK db=0.0ms idle=3.1ms
PRAGMA index_info(comments_approved_index) []

13:11:45.896 [debug] QUERY OK db=0.0ms idle=3.1ms
PRAGMA index_info(comments_post_id_index) []

13:11:45.897 [debug] QUERY OK db=0.0ms idle=3.2ms
PRAGMA index_info(comments_user_id_index) []

13:11:45.897 [debug] QUERY OK db=0.0ms idle=3.2ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

13:11:45.897 [debug] QUERY OK db=0.0ms idle=3.8ms
PRAGMA table_info(comments) []

13:11:45.910 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

13:11:45.910 [debug] QUERY OK db=0.0ms idle=16.6ms
PRAGMA foreign_key_list(posts) []

13:11:45.910 [debug] QUERY OK db=0.0ms idle=16.7ms
PRAGMA index_list(posts) []

13:11:45.910 [debug] QUERY OK db=0.0ms idle=16.1ms
PRAGMA index_info(posts_title_index) []

13:11:45.910 [debug] QUERY OK db=0.0ms idle=13.8ms
PRAGMA index_info(posts_published_index) []

13:11:45.910 [debug] QUERY OK db=0.0ms idle=13.8ms
PRAGMA index_info(posts_user_id_index) []

13:11:45.910 [debug] QUERY OK db=0.0ms idle=13.8ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

13:11:45.910 [debug] QUERY OK db=0.0ms idle=13.8ms
PRAGMA table_info(posts) []

13:11:45.911 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

13:11:45.911 [debug] QUERY OK db=0.0ms idle=14.6ms
PRAGMA foreign_key_list(users) []

13:11:45.911 [debug] QUERY OK db=0.0ms idle=14.6ms
PRAGMA index_list(users) []

13:11:45.911 [debug] QUERY OK db=0.0ms idle=14.1ms
PRAGMA index_info(users_active_index) []

13:11:45.911 [debug] QUERY OK db=0.0ms idle=1.2ms
PRAGMA index_info(users_email_index) []

13:11:45.911 [debug] QUERY OK db=0.0ms idle=1.2ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

13:11:45.911 [debug] QUERY OK db=0.0ms idle=1.2ms
PRAGMA table_info(users) []

13:11:46.866 [info] Refreshing Drops.Relation cache...

13:11:46.870 [info] Repositories: [Sample.Repos.Sqlite]

13:11:46.870 [info] Tables: all

13:11:46.870 [info] Processing repository: Sample.Repos.Sqlite

13:11:46.873 [info]   Cache cleared for Sample.Repos.Sqlite

13:11:46.883 [debug] QUERY OK db=0.6ms decode=0.5ms idle=2.6ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

13:11:46.884 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

13:11:46.884 [debug] QUERY OK db=0.0ms idle=5.0ms
PRAGMA foreign_key_list(comments) []

13:11:46.884 [debug] QUERY OK db=0.0ms idle=5.1ms
PRAGMA index_list(comments) []

13:11:46.884 [debug] QUERY OK db=0.0ms idle=5.1ms
PRAGMA index_info(comments_approved_index) []

13:11:46.884 [debug] QUERY OK db=0.0ms idle=5.2ms
PRAGMA index_info(comments_post_id_index) []

13:11:46.884 [debug] QUERY OK db=0.0ms idle=5.3ms
PRAGMA index_info(comments_user_id_index) []

13:11:46.885 [debug] QUERY OK db=0.0ms idle=5.4ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

13:11:46.885 [debug] QUERY OK db=0.0ms idle=6.0ms
PRAGMA table_info(comments) []

13:11:46.897 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

13:11:46.897 [debug] QUERY OK db=0.0ms idle=17.8ms
PRAGMA foreign_key_list(posts) []

13:11:46.897 [debug] QUERY OK db=0.0ms idle=17.9ms
PRAGMA index_list(posts) []

13:11:46.897 [debug] QUERY OK db=0.0ms idle=14.7ms
PRAGMA index_info(posts_title_index) []

13:11:46.897 [debug] QUERY OK db=0.0ms idle=12.9ms
PRAGMA index_info(posts_published_index) []

13:11:46.897 [debug] QUERY OK db=0.0ms idle=12.9ms
PRAGMA index_info(posts_user_id_index) []

13:11:46.897 [debug] QUERY OK db=0.0ms idle=12.8ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

13:11:46.897 [debug] QUERY OK db=0.0ms idle=12.8ms
PRAGMA table_info(posts) []

13:11:46.898 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

13:11:46.898 [debug] QUERY OK db=0.0ms idle=13.6ms
PRAGMA foreign_key_list(users) []

13:11:46.898 [debug] QUERY OK db=0.0ms idle=13.7ms
PRAGMA index_list(users) []

13:11:46.898 [debug] QUERY OK db=0.0ms idle=13.1ms
PRAGMA index_info(users_active_index) []

13:11:46.898 [debug] QUERY OK db=0.0ms idle=1.3ms
PRAGMA index_info(users_email_index) []

13:11:46.898 [debug] QUERY OK db=0.0ms idle=1.3ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

13:11:46.898 [debug] QUERY OK db=0.0ms idle=1.4ms
PRAGMA table_info(users) []

13:11:46.899 [info]   Cache warmed up for 3 tables

13:11:46.900 [info] Successful: 1

13:11:46.900 [info] Failed: 0

13:11:47.927 [info] Refreshing Drops.Relation cache...

13:11:47.934 [info] Repositories: [Sample.Repos.Sqlite]

13:11:47.934 [info] Tables: all

13:11:47.934 [info] Processing repository: Sample.Repos.Sqlite

13:11:47.938 [info]   Cache cleared for Sample.Repos.Sqlite

13:11:47.943 [debug] QUERY OK db=0.6ms decode=0.5ms idle=7.8ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

13:11:47.944 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

13:11:47.944 [debug] QUERY OK db=0.0ms idle=10.6ms
PRAGMA foreign_key_list(comments) []

13:11:47.944 [debug] QUERY OK db=0.0ms idle=10.7ms
PRAGMA index_list(comments) []

13:11:47.944 [debug] QUERY OK db=0.0ms idle=10.7ms
PRAGMA index_info(comments_approved_index) []

13:11:47.944 [debug] QUERY OK db=0.0ms idle=10.8ms
PRAGMA index_info(comments_post_id_index) []

13:11:47.944 [debug] QUERY OK db=0.0ms idle=10.8ms
PRAGMA index_info(comments_user_id_index) []

13:11:47.945 [debug] QUERY OK db=0.0ms idle=10.9ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

13:11:47.945 [debug] QUERY OK db=0.0ms idle=11.6ms
PRAGMA table_info(comments) []

13:11:47.962 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

13:11:47.962 [debug] QUERY OK db=0.0ms idle=28.6ms
PRAGMA foreign_key_list(posts) []

13:11:47.962 [debug] QUERY OK db=0.0ms idle=28.4ms
PRAGMA index_list(posts) []

13:11:47.962 [debug] QUERY OK db=0.0ms idle=20.2ms
PRAGMA index_info(posts_title_index) []

13:11:47.962 [debug] QUERY OK db=0.0ms idle=18.1ms
PRAGMA index_info(posts_published_index) []

13:11:47.962 [debug] QUERY OK db=0.0ms idle=18.0ms
PRAGMA index_info(posts_user_id_index) []

13:11:47.962 [debug] QUERY OK db=0.0ms idle=18.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

13:11:47.963 [debug] QUERY OK db=0.0ms idle=18.1ms
PRAGMA table_info(posts) []

13:11:47.963 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

13:11:47.963 [debug] QUERY OK db=0.0ms idle=18.8ms
PRAGMA foreign_key_list(users) []

13:11:47.963 [debug] QUERY OK db=0.0ms idle=18.8ms
PRAGMA index_list(users) []

13:11:47.963 [debug] QUERY OK db=0.0ms idle=18.1ms
PRAGMA index_info(users_active_index) []

13:11:47.963 [debug] QUERY OK db=0.0ms idle=1.1ms
PRAGMA index_info(users_email_index) []

13:11:47.963 [debug] QUERY OK db=0.0ms idle=1.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

13:11:47.963 [debug] QUERY OK db=0.0ms idle=1.1ms
PRAGMA table_info(users) []

13:11:47.965 [info]   Cache warmed up for 3 tables

13:11:47.966 [info] Successful: 1

13:11:47.966 [info] Failed: 0

13:11:48.572 [info] Refreshing Drops.Relation cache...

13:11:48.576 [info] Repositories: [Sample.Repos.Sqlite]

13:11:48.576 [info] Tables: all

13:11:48.576 [info] Processing repository: Sample.Repos.Sqlite

13:11:48.579 [info]   Cache cleared for Sample.Repos.Sqlite

13:11:48.583 [debug] QUERY OK db=0.4ms decode=0.4ms idle=3.3ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

13:11:48.584 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

13:11:48.584 [debug] QUERY OK db=0.0ms idle=5.5ms
PRAGMA foreign_key_list(comments) []

13:11:48.584 [debug] QUERY OK db=0.0ms idle=5.3ms
PRAGMA index_list(comments) []

13:11:48.584 [debug] QUERY OK db=0.0ms idle=5.3ms
PRAGMA index_info(comments_approved_index) []

13:11:48.584 [debug] QUERY OK db=0.0ms idle=5.4ms
PRAGMA index_info(comments_post_id_index) []

13:11:48.584 [debug] QUERY OK db=0.0ms idle=5.4ms
PRAGMA index_info(comments_user_id_index) []

13:11:48.585 [debug] QUERY OK db=0.0ms idle=5.4ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

13:11:48.585 [debug] QUERY OK db=0.0ms idle=6.0ms
PRAGMA table_info(comments) []

13:11:48.595 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

13:11:48.595 [debug] QUERY OK db=0.0ms idle=16.0ms
PRAGMA foreign_key_list(posts) []

13:11:48.595 [debug] QUERY OK db=0.0ms idle=16.1ms
PRAGMA index_list(posts) []

13:11:48.595 [debug] QUERY OK db=0.0ms idle=12.6ms
PRAGMA index_info(posts_title_index) []

13:11:48.595 [debug] QUERY OK db=0.0ms idle=11.0ms
PRAGMA index_info(posts_published_index) []

13:11:48.595 [debug] QUERY OK db=0.0ms idle=11.0ms
PRAGMA index_info(posts_user_id_index) []

13:11:48.595 [debug] QUERY OK db=0.0ms idle=11.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

13:11:48.595 [debug] QUERY OK db=0.0ms idle=11.0ms
PRAGMA table_info(posts) []

13:11:48.596 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

13:11:48.596 [debug] QUERY OK db=0.0ms idle=11.8ms
PRAGMA foreign_key_list(users) []

13:11:48.596 [debug] QUERY OK db=0.0ms idle=11.8ms
PRAGMA index_list(users) []

13:11:48.596 [debug] QUERY OK db=0.0ms idle=11.3ms
PRAGMA index_info(users_active_index) []

13:11:48.596 [debug] QUERY OK db=0.0ms idle=1.3ms
PRAGMA index_info(users_email_index) []

13:11:48.596 [debug] QUERY OK db=0.0ms idle=1.3ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

13:11:48.596 [debug] QUERY OK db=0.0ms idle=1.3ms
PRAGMA table_info(users) []

13:11:48.598 [info]   Cache warmed up for 3 tables

13:11:48.598 [info] Successful: 1

13:11:48.598 [info] Failed: 0

13:11:49.206 [info] Refreshing Drops.Relation cache...

13:11:49.209 [info] Repositories: [Sample.Repos.Sqlite]

13:11:49.211 [info] Tables: ["users", "posts"]

13:11:49.211 [info] Processing repository: Sample.Repos.Sqlite

13:11:49.215 [info]   Cache cleared for Sample.Repos.Sqlite

13:11:49.217 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

13:11:49.222 [debug] QUERY OK db=0.5ms decode=0.5ms idle=4.2ms
PRAGMA foreign_key_list(users) []

13:11:49.222 [debug] QUERY OK db=0.0ms idle=5.9ms
PRAGMA index_list(users) []

13:11:49.222 [debug] QUERY OK db=0.0ms idle=6.0ms
PRAGMA index_info(users_active_index) []

13:11:49.222 [debug] QUERY OK db=0.0ms idle=6.0ms
PRAGMA index_info(users_email_index) []

13:11:49.222 [debug] QUERY OK db=0.0ms idle=6.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

13:11:49.222 [debug] QUERY OK db=0.0ms idle=6.0ms
PRAGMA table_info(users) []

13:11:49.234 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

13:11:49.234 [debug] QUERY OK db=0.0ms idle=17.8ms
PRAGMA foreign_key_list(posts) []

13:11:49.234 [debug] QUERY OK db=0.0ms idle=17.9ms
PRAGMA index_list(posts) []

13:11:49.234 [debug] QUERY OK db=0.0ms idle=17.9ms
PRAGMA index_info(posts_title_index) []

13:11:49.234 [debug] QUERY OK db=0.0ms idle=18.0ms
PRAGMA index_info(posts_published_index) []

13:11:49.234 [debug] QUERY OK db=0.0ms idle=13.4ms
PRAGMA index_info(posts_user_id_index) []

13:11:49.234 [debug] QUERY OK db=0.0ms idle=12.2ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

13:11:49.234 [debug] QUERY OK db=0.0ms idle=12.2ms
PRAGMA table_info(posts) []

13:11:49.236 [info]   Cache warmed up for 2 tables

13:11:49.236 [info] Successful: 1

13:11:49.236 [info] Failed: 0

13:11:49.698 [info] Refreshing Drops.Relation cache...

13:11:49.704 [info] Repositories: [Sample.Repos.Sqlite]

13:11:49.705 [info] Tables: ["''"]

13:11:49.705 [info] Processing repository: Sample.Repos.Sqlite

13:11:49.710 [info]   Cache cleared for Sample.Repos.Sqlite

13:11:49.712 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.'' (not cached: {:file_read, :enoent})

13:11:49.718 [debug] QUERY OK db=0.7ms decode=0.8ms queue=0.1ms idle=9.9ms
PRAGMA foreign_key_list('') []

13:11:49.718 [debug] QUERY OK db=0.0ms idle=12.5ms
PRAGMA index_list('') []

13:11:49.719 [debug] QUERY OK db=0.0ms idle=12.6ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["''"]

13:11:49.719 [debug] QUERY OK db=0.0ms idle=12.7ms
PRAGMA table_info('') []

13:11:49.739 [info]   Cache warmed up for 1 tables

13:11:49.741 [info] Successful: 1

13:11:49.741 [info] Failed: 0

13:11:52.419 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

13:11:52.430 [debug] QUERY OK db=0.7ms decode=0.6ms idle=1.7ms
PRAGMA foreign_key_list(users) []

13:11:52.430 [debug] QUERY OK db=0.0ms idle=4.7ms
PRAGMA index_list(users) []

13:11:52.430 [debug] QUERY OK db=0.0ms idle=4.8ms
PRAGMA index_info(users_active_index) []

13:11:52.431 [debug] QUERY OK db=0.0ms idle=4.9ms
PRAGMA index_info(users_email_index) []

13:11:52.431 [debug] QUERY OK db=0.0ms idle=4.9ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

13:11:52.431 [debug] QUERY OK db=0.0ms idle=5.7ms
PRAGMA table_info(users) []

13:11:52.473 [debug] QUERY OK db=0.0ms idle=47.3ms
PRAGMA foreign_key_list(users) []

13:11:52.473 [debug] QUERY OK db=0.0ms idle=47.4ms
PRAGMA index_list(users) []

13:11:52.473 [debug] QUERY OK db=0.0ms idle=47.5ms
PRAGMA index_info(users_active_index) []

13:11:52.473 [debug] QUERY OK db=0.0ms idle=47.6ms
PRAGMA index_info(users_email_index) []

13:11:52.473 [debug] QUERY OK db=0.0ms idle=45.2ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

13:11:52.473 [debug] QUERY OK db=0.0ms idle=43.0ms
PRAGMA table_info(users) []

13:11:53.203 [debug] QUERY OK db=1.0ms decode=0.7ms queue=1.2ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

13:11:53.206 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

13:11:53.206 [debug] QUERY OK db=0.0ms idle=6.0ms
PRAGMA foreign_key_list(comments) []

13:11:53.206 [debug] QUERY OK db=0.0ms idle=6.1ms
PRAGMA index_list(comments) []

13:11:53.206 [debug] QUERY OK db=0.0ms idle=6.2ms
PRAGMA index_info(comments_approved_index) []

13:11:53.206 [debug] QUERY OK db=0.0ms idle=6.2ms
PRAGMA index_info(comments_post_id_index) []

13:11:53.206 [debug] QUERY OK db=0.0ms idle=6.3ms
PRAGMA index_info(comments_user_id_index) []

13:11:53.207 [debug] QUERY OK db=0.0ms idle=6.4ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

13:11:53.207 [debug] QUERY OK db=0.0ms idle=7.1ms
PRAGMA table_info(comments) []

13:11:53.253 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

13:11:53.254 [debug] QUERY OK db=0.1ms idle=53.9ms
PRAGMA foreign_key_list(posts) []

13:11:53.254 [debug] QUERY OK db=0.1ms idle=54.2ms
PRAGMA index_list(posts) []

13:11:53.254 [debug] QUERY OK db=0.0ms idle=53.3ms
PRAGMA index_info(posts_title_index) []

13:11:53.254 [debug] QUERY OK db=0.0ms idle=48.3ms
PRAGMA index_info(posts_published_index) []

13:11:53.254 [debug] QUERY OK db=0.0ms idle=48.2ms
PRAGMA index_info(posts_user_id_index) []

13:11:53.254 [debug] QUERY OK db=0.0ms idle=48.2ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

13:11:53.254 [debug] QUERY OK db=0.0ms idle=48.3ms
PRAGMA table_info(posts) []

13:11:53.748 [debug] QUERY OK db=0.7ms decode=0.5ms queue=1.1ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

13:11:54.321 [debug] QUERY OK db=0.6ms decode=0.5ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

13:11:55.918 [debug] QUERY OK db=0.5ms decode=0.5ms queue=0.1ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

13:11:56.550 [debug] QUERY OK db=0.5ms decode=0.5ms idle=30.3ms
PRAGMA foreign_key_list(users) []

13:11:56.552 [debug] QUERY OK db=0.1ms idle=33.6ms
PRAGMA index_list(users) []

13:11:56.552 [debug] QUERY OK db=0.0ms idle=33.8ms
PRAGMA index_info(users_active_index) []

13:11:56.552 [debug] QUERY OK db=0.0ms idle=33.9ms
PRAGMA index_info(users_email_index) []

13:11:56.552 [debug] QUERY OK db=0.0ms idle=33.9ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

13:11:56.552 [debug] QUERY OK db=0.1ms idle=34.5ms
PRAGMA table_info(users) []

13:12:37.077 [debug] QUERY OK db=1.2ms decode=0.6ms queue=2.8ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

13:12:37.712 [debug] QUERY OK db=1.1ms decode=0.6ms queue=1.3ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

13:12:38.355 [debug] QUERY OK db=0.6ms decode=0.4ms idle=28.1ms
PRAGMA foreign_key_list(users) []

13:12:38.356 [debug] QUERY OK db=0.0ms idle=31.1ms
PRAGMA index_list(users) []

13:12:38.356 [debug] QUERY OK db=0.0ms idle=31.2ms
PRAGMA index_info(users_active_index) []

13:12:38.357 [debug] QUERY OK db=0.0ms idle=31.3ms
PRAGMA index_info(users_email_index) []

13:12:38.357 [debug] QUERY OK db=0.0ms idle=31.3ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

13:12:38.357 [debug] QUERY OK db=0.0ms idle=32.0ms
PRAGMA table_info(users) []

13:12:38.800 [debug] QUERY OK db=0.7ms decode=0.6ms queue=1.0ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

13:12:39.318 [debug] QUERY OK db=0.5ms decode=0.6ms queue=1.4ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

13:12:40.428 [debug] QUERY OK db=0.6ms decode=0.6ms idle=30.6ms
PRAGMA foreign_key_list(users) []

13:12:40.429 [debug] QUERY OK db=0.0ms idle=33.9ms
PRAGMA index_list(users) []

13:12:40.429 [debug] QUERY OK db=0.0ms idle=34.0ms
PRAGMA index_info(users_active_index) []

13:12:40.429 [debug] QUERY OK db=0.0ms idle=34.0ms
PRAGMA index_info(users_email_index) []

13:12:40.429 [debug] QUERY OK db=0.0ms idle=34.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

13:12:40.430 [debug] QUERY OK db=0.0ms idle=34.8ms
PRAGMA table_info(users) []

13:12:41.357 [info] Refreshing Drops.Relation cache...

13:12:41.362 [info] Repositories: [Sample.Repos.Sqlite]

13:12:41.362 [info] Tables: all

13:12:41.362 [info] Processing repository: Sample.Repos.Sqlite

13:12:41.369 [info]   Cache cleared for Sample.Repos.Sqlite

13:12:41.375 [debug] QUERY OK db=0.8ms decode=0.6ms idle=5.2ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

13:12:41.376 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

13:12:41.376 [debug] QUERY OK db=0.1ms queue=0.1ms idle=8.2ms
PRAGMA foreign_key_list(comments) []

13:12:41.376 [debug] QUERY OK db=0.0ms idle=8.5ms
PRAGMA index_list(comments) []

13:12:41.376 [debug] QUERY OK db=0.0ms idle=8.6ms
PRAGMA index_info(comments_approved_index) []

13:12:41.376 [debug] QUERY OK db=0.0ms idle=8.7ms
PRAGMA index_info(comments_post_id_index) []

13:12:41.376 [debug] QUERY OK db=0.0ms idle=8.8ms
PRAGMA index_info(comments_user_id_index) []

13:12:41.377 [debug] QUERY OK db=0.0ms idle=8.8ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

13:12:41.377 [debug] QUERY OK db=0.1ms idle=9.5ms
PRAGMA table_info(comments) []

13:12:41.391 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

13:12:41.391 [debug] QUERY OK db=0.0ms idle=23.8ms
PRAGMA foreign_key_list(posts) []

13:12:41.391 [debug] QUERY OK db=0.0ms idle=23.9ms
PRAGMA index_list(posts) []

13:12:41.391 [debug] QUERY OK db=0.0ms idle=17.8ms
PRAGMA index_info(posts_title_index) []

13:12:41.391 [debug] QUERY OK db=0.0ms idle=15.5ms
PRAGMA index_info(posts_published_index) []

13:12:41.392 [debug] QUERY OK db=0.0ms idle=15.4ms
PRAGMA index_info(posts_user_id_index) []

13:12:41.392 [debug] QUERY OK db=0.0ms idle=15.4ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

13:12:41.392 [debug] QUERY OK db=0.0ms idle=15.4ms
PRAGMA table_info(posts) []

13:12:41.392 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

13:12:41.393 [debug] QUERY OK db=0.0ms idle=16.2ms
PRAGMA foreign_key_list(users) []

13:12:41.393 [debug] QUERY OK db=0.0ms idle=16.2ms
PRAGMA index_list(users) []

13:12:41.393 [debug] QUERY OK db=0.0ms idle=15.4ms
PRAGMA index_info(users_active_index) []

13:12:41.393 [debug] QUERY OK db=0.0ms idle=1.3ms
PRAGMA index_info(users_email_index) []

13:12:41.393 [debug] QUERY OK db=0.0ms idle=1.2ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

13:12:41.393 [debug] QUERY OK db=0.0ms idle=1.3ms
PRAGMA table_info(users) []

13:12:41.394 [info]   Cache warmed up for 3 tables

13:12:41.395 [info] Successful: 1

13:12:41.395 [info] Failed: 0

13:12:42.013 [info] Refreshing Drops.Relation cache...

13:12:42.018 [info] Repositories: [Sample.Repos.Sqlite]

13:12:42.020 [info] Tables: ["users", "posts"]

13:12:42.020 [info] Processing repository: Sample.Repos.Sqlite

13:12:42.022 [info]   Cache cleared for Sample.Repos.Sqlite

13:12:42.024 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

13:12:42.030 [debug] QUERY OK db=0.7ms decode=0.5ms idle=4.9ms
PRAGMA foreign_key_list(users) []

13:12:42.030 [debug] QUERY OK db=0.0ms idle=6.7ms
PRAGMA index_list(users) []

13:12:42.030 [debug] QUERY OK db=0.0ms idle=6.8ms
PRAGMA index_info(users_active_index) []

13:12:42.031 [debug] QUERY OK db=0.0ms idle=6.9ms
PRAGMA index_info(users_email_index) []

13:12:42.031 [debug] QUERY OK db=0.0ms idle=7.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

13:12:42.031 [debug] QUERY OK db=0.1ms idle=7.0ms
PRAGMA table_info(users) []

13:12:42.044 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

13:12:42.044 [debug] QUERY OK db=0.0ms idle=20.1ms
PRAGMA foreign_key_list(posts) []

13:12:42.044 [debug] QUERY OK db=0.0ms idle=20.2ms
PRAGMA index_list(posts) []

13:12:42.044 [debug] QUERY OK db=0.0ms idle=20.2ms
PRAGMA index_info(posts_title_index) []

13:12:42.044 [debug] QUERY OK db=0.0ms idle=20.3ms
PRAGMA index_info(posts_published_index) []

13:12:42.044 [debug] QUERY OK db=0.0ms idle=14.9ms
PRAGMA index_info(posts_user_id_index) []

13:12:42.044 [debug] QUERY OK db=0.0ms idle=13.8ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

13:12:42.044 [debug] QUERY OK db=0.0ms idle=13.7ms
PRAGMA table_info(posts) []

13:12:42.046 [info]   Cache warmed up for 2 tables

13:12:42.046 [info] Successful: 1

13:12:42.046 [info] Failed: 0

13:12:43.092 [info] Refreshing Drops.Relation cache...

13:12:43.095 [info] Repositories: [Sample.Repos.Sqlite]

13:12:43.095 [info] Tables: all

13:12:43.096 [info] Processing repository: Sample.Repos.Sqlite

13:12:43.097 [info]   Cache cleared for Sample.Repos.Sqlite

13:12:43.107 [debug] QUERY OK db=0.6ms decode=0.7ms idle=3.2ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

13:12:43.108 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

13:12:43.108 [debug] QUERY OK db=0.0ms idle=6.0ms
PRAGMA foreign_key_list(comments) []

13:12:43.108 [debug] QUERY OK db=0.0ms idle=6.1ms
PRAGMA index_list(comments) []

13:12:43.109 [debug] QUERY OK db=0.0ms idle=6.2ms
PRAGMA index_info(comments_approved_index) []

13:12:43.109 [debug] QUERY OK db=0.0ms idle=6.3ms
PRAGMA index_info(comments_post_id_index) []

13:12:43.109 [debug] QUERY OK db=0.0ms idle=6.3ms
PRAGMA index_info(comments_user_id_index) []

13:12:43.109 [debug] QUERY OK db=0.0ms idle=6.4ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

13:12:43.110 [debug] QUERY OK db=0.0ms idle=7.2ms
PRAGMA table_info(comments) []

13:12:43.124 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

13:12:43.124 [debug] QUERY OK db=0.0ms idle=21.6ms
PRAGMA foreign_key_list(posts) []

13:12:43.124 [debug] QUERY OK db=0.0ms idle=21.7ms
PRAGMA index_list(posts) []

13:12:43.124 [debug] QUERY OK db=0.0ms idle=17.9ms
PRAGMA index_info(posts_title_index) []

13:12:43.124 [debug] QUERY OK db=0.0ms idle=15.7ms
PRAGMA index_info(posts_published_index) []

13:12:43.124 [debug] QUERY OK db=0.0ms idle=15.7ms
PRAGMA index_info(posts_user_id_index) []

13:12:43.124 [debug] QUERY OK db=0.0ms idle=15.7ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

13:12:43.124 [debug] QUERY OK db=0.0ms idle=15.7ms
PRAGMA table_info(posts) []

13:12:43.125 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

13:12:43.125 [debug] QUERY OK db=0.0ms idle=16.5ms
PRAGMA foreign_key_list(users) []

13:12:43.125 [debug] QUERY OK db=0.0ms idle=16.5ms
PRAGMA index_list(users) []

13:12:43.125 [debug] QUERY OK db=0.0ms idle=15.8ms
PRAGMA index_info(users_active_index) []

13:12:43.125 [debug] QUERY OK db=0.0ms idle=1.4ms
PRAGMA index_info(users_email_index) []

13:12:43.126 [debug] QUERY OK db=0.0ms idle=1.4ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

13:12:43.126 [debug] QUERY OK db=0.0ms idle=1.4ms
PRAGMA table_info(users) []

13:12:43.127 [info]   Cache warmed up for 3 tables

13:12:43.127 [info] Successful: 1

13:12:43.127 [info] Failed: 0

13:12:43.538 [info] Refreshing Drops.Relation cache...

13:12:43.543 [info] Repositories: [Sample.Repos.Sqlite]

13:12:43.545 [info] Tables: ["non_existent_table"]

13:12:43.545 [info] Processing repository: Sample.Repos.Sqlite

13:12:43.549 [info]   Cache cleared for Sample.Repos.Sqlite

13:12:43.551 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.non_existent_table (not cached: {:file_read, :enoent})

13:12:43.555 [debug] QUERY OK db=0.4ms decode=0.4ms idle=9.7ms
PRAGMA foreign_key_list(non_existent_table) []

13:12:43.555 [debug] QUERY OK db=0.0ms idle=11.0ms
PRAGMA index_list(non_existent_table) []

13:12:43.555 [debug] QUERY OK db=0.0ms idle=11.1ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["non_existent_table"]

13:12:43.556 [debug] QUERY OK db=0.1ms idle=11.1ms
PRAGMA table_info(non_existent_table) []

13:12:43.567 [info]   Cache warmed up for 1 tables

13:12:43.568 [info] Successful: 1

13:12:43.568 [info] Failed: 0

13:12:44.189 [debug] QUERY OK db=0.6ms decode=0.5ms queue=0.1ms idle=0.0ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

13:12:44.190 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

13:12:44.190 [debug] QUERY OK db=0.0ms idle=3.2ms
PRAGMA foreign_key_list(comments) []

13:12:44.190 [debug] QUERY OK db=0.0ms idle=3.2ms
PRAGMA index_list(comments) []

13:12:44.190 [debug] QUERY OK db=0.0ms idle=3.3ms
PRAGMA index_info(comments_approved_index) []

13:12:44.190 [debug] QUERY OK db=0.0ms idle=3.3ms
PRAGMA index_info(comments_post_id_index) []

13:12:44.190 [debug] QUERY OK db=0.0ms idle=3.4ms
PRAGMA index_info(comments_user_id_index) []

13:12:44.191 [debug] QUERY OK db=0.0ms idle=3.5ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

13:12:44.191 [debug] QUERY OK db=0.1ms idle=4.2ms
PRAGMA table_info(comments) []

13:12:44.207 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

13:12:44.207 [debug] QUERY OK db=0.0ms idle=20.2ms
PRAGMA foreign_key_list(posts) []

13:12:44.207 [debug] QUERY OK db=0.0ms idle=20.2ms
PRAGMA index_list(posts) []

13:12:44.207 [debug] QUERY OK db=0.0ms idle=19.6ms
PRAGMA index_info(posts_title_index) []

13:12:44.207 [debug] QUERY OK db=0.0ms idle=17.1ms
PRAGMA index_info(posts_published_index) []

13:12:44.207 [debug] QUERY OK db=0.0ms idle=17.1ms
PRAGMA index_info(posts_user_id_index) []

13:12:44.207 [debug] QUERY OK db=0.0ms idle=17.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

13:12:44.207 [debug] QUERY OK db=0.0ms idle=17.1ms
PRAGMA table_info(posts) []

13:12:44.208 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

13:12:44.208 [debug] QUERY OK db=0.0ms idle=18.0ms
PRAGMA foreign_key_list(users) []

13:12:44.208 [debug] QUERY OK db=0.0ms idle=18.0ms
PRAGMA index_list(users) []

13:12:44.208 [debug] QUERY OK db=0.0ms idle=17.2ms
PRAGMA index_info(users_active_index) []

13:12:44.208 [debug] QUERY OK db=0.0ms idle=1.4ms
PRAGMA index_info(users_email_index) []

13:12:44.208 [debug] QUERY OK db=0.0ms idle=1.3ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

13:12:44.208 [debug] QUERY OK db=0.0ms idle=1.3ms
PRAGMA table_info(users) []

13:12:44.814 [info] Refreshing Drops.Relation cache...

13:12:44.817 [info] Repositories: [Sample.Repos.Sqlite]

13:12:44.817 [info] Tables: all

13:12:44.818 [info] Processing repository: Sample.Repos.Sqlite

13:12:44.819 [info]   Cache cleared for Sample.Repos.Sqlite

13:12:44.826 [debug] QUERY OK db=0.5ms decode=0.5ms idle=1.4ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

13:12:44.826 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

13:12:44.826 [debug] QUERY OK db=0.0ms idle=3.8ms
PRAGMA foreign_key_list(comments) []

13:12:44.826 [debug] QUERY OK db=0.0ms idle=3.8ms
PRAGMA index_list(comments) []

13:12:44.827 [debug] QUERY OK db=0.0ms idle=3.9ms
PRAGMA index_info(comments_approved_index) []

13:12:44.827 [debug] QUERY OK db=0.0ms idle=3.9ms
PRAGMA index_info(comments_post_id_index) []

13:12:44.827 [debug] QUERY OK db=0.0ms idle=3.9ms
PRAGMA index_info(comments_user_id_index) []

13:12:44.827 [debug] QUERY OK db=0.0ms idle=4.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

13:12:44.827 [debug] QUERY OK db=0.0ms idle=4.5ms
PRAGMA table_info(comments) []

13:12:44.838 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

13:12:44.838 [debug] QUERY OK db=0.0ms idle=15.4ms
PRAGMA foreign_key_list(posts) []

13:12:44.838 [debug] QUERY OK db=0.0ms idle=15.5ms
PRAGMA index_list(posts) []

13:12:44.838 [debug] QUERY OK db=0.0ms idle=13.6ms
PRAGMA index_info(posts_title_index) []

13:12:44.838 [debug] QUERY OK db=0.0ms idle=11.9ms
PRAGMA index_info(posts_published_index) []

13:12:44.838 [debug] QUERY OK db=0.0ms idle=11.9ms
PRAGMA index_info(posts_user_id_index) []

13:12:44.839 [debug] QUERY OK db=0.0ms idle=11.9ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

13:12:44.839 [debug] QUERY OK db=0.0ms idle=12.0ms
PRAGMA table_info(posts) []

13:12:44.840 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

13:12:44.840 [debug] QUERY OK db=0.0ms idle=13.3ms
PRAGMA foreign_key_list(users) []

13:12:44.840 [debug] QUERY OK db=0.0ms idle=13.3ms
PRAGMA index_list(users) []

13:12:44.840 [debug] QUERY OK db=0.0ms idle=12.8ms
PRAGMA index_info(users_active_index) []

13:12:44.840 [debug] QUERY OK db=0.0ms idle=2.0ms
PRAGMA index_info(users_email_index) []

13:12:44.840 [debug] QUERY OK db=0.0ms idle=2.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

13:12:44.840 [debug] QUERY OK db=0.0ms idle=2.0ms
PRAGMA table_info(users) []

13:12:44.844 [info]   Cache warmed up for 3 tables

13:12:44.844 [info] Successful: 1

13:12:44.844 [info] Failed: 0

13:12:45.640 [info] Refreshing Drops.Relation cache...

13:12:45.644 [info] Repositories: [Sample.Repos.Sqlite]

13:12:45.645 [info] Tables: ["''"]

13:12:45.645 [info] Processing repository: Sample.Repos.Sqlite

13:12:45.653 [info]   Cache cleared for Sample.Repos.Sqlite

13:12:45.655 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.'' (not cached: {:file_read, :enoent})

13:12:45.659 [debug] QUERY OK db=0.4ms decode=0.5ms idle=7.2ms
PRAGMA foreign_key_list('') []

13:12:45.659 [debug] QUERY OK db=0.0ms idle=8.7ms
PRAGMA index_list('') []

13:12:45.659 [debug] QUERY OK db=0.0ms idle=8.7ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["''"]

13:12:45.659 [debug] QUERY OK db=0.0ms idle=8.8ms
PRAGMA table_info('') []

13:12:45.669 [info]   Cache warmed up for 1 tables

13:12:45.670 [info] Successful: 1

13:12:45.670 [info] Failed: 0

13:12:46.277 [info] Refreshing Drops.Relation cache...

13:12:46.279 [info] Repositories: [Sample.Repos.Sqlite]

13:12:46.279 [info] Tables: all

13:12:46.279 [info] Processing repository: Sample.Repos.Sqlite

13:12:46.281 [info]   Cache cleared for Sample.Repos.Sqlite

13:12:46.288 [debug] QUERY OK db=0.5ms decode=0.5ms idle=2.1ms
SELECT name
FROM sqlite_master
WHERE type = 'table'
AND name NOT LIKE 'sqlite_%'
AND name != 'schema_migrations'
ORDER BY name
 []

13:12:46.288 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.comments (not cached: {:file_read, :enoent})

13:12:46.289 [debug] QUERY OK db=0.0ms idle=4.0ms
PRAGMA foreign_key_list(comments) []

13:12:46.289 [debug] QUERY OK db=0.0ms idle=4.1ms
PRAGMA index_list(comments) []

13:12:46.289 [debug] QUERY OK db=0.0ms idle=4.2ms
PRAGMA index_info(comments_approved_index) []

13:12:46.289 [debug] QUERY OK db=0.0ms idle=4.1ms
PRAGMA index_info(comments_post_id_index) []

13:12:46.289 [debug] QUERY OK db=0.0ms idle=4.2ms
PRAGMA index_info(comments_user_id_index) []

13:12:46.289 [debug] QUERY OK db=0.0ms idle=4.3ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["comments"]

13:12:46.290 [debug] QUERY OK db=0.1ms idle=4.9ms
PRAGMA table_info(comments) []

13:12:46.306 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.posts (not cached: {:file_read, :enoent})

13:12:46.306 [debug] QUERY OK db=0.0ms idle=21.8ms
PRAGMA foreign_key_list(posts) []

13:12:46.307 [debug] QUERY OK db=0.0ms idle=21.9ms
PRAGMA index_list(posts) []

13:12:46.307 [debug] QUERY OK db=0.0ms idle=19.5ms
PRAGMA index_info(posts_title_index) []

13:12:46.307 [debug] QUERY OK db=0.0ms idle=18.1ms
PRAGMA index_info(posts_published_index) []

13:12:46.307 [debug] QUERY OK db=0.0ms idle=18.1ms
PRAGMA index_info(posts_user_id_index) []

13:12:46.307 [debug] QUERY OK db=0.0ms idle=18.0ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["posts"]

13:12:46.307 [debug] QUERY OK db=0.0ms idle=18.0ms
PRAGMA table_info(posts) []

13:12:46.308 [debug] Schema cache miss for Elixir.Sample.Repos.Sqlite.users (not cached: {:file_read, :enoent})

13:12:46.308 [debug] QUERY OK db=0.0ms idle=18.8ms
PRAGMA foreign_key_list(users) []

13:12:46.308 [debug] QUERY OK db=0.0ms idle=18.9ms
PRAGMA index_list(users) []

13:12:46.308 [debug] QUERY OK db=0.0ms idle=18.2ms
PRAGMA index_info(users_active_index) []

13:12:46.308 [debug] QUERY OK db=0.0ms idle=1.3ms
PRAGMA index_info(users_email_index) []

13:12:46.308 [debug] QUERY OK db=0.0ms idle=1.3ms
SELECT sql FROM sqlite_master
  WHERE type = 'table' AND name = ?
 ["users"]

13:12:46.308 [debug] QUERY OK db=0.0ms idle=1.2ms
PRAGMA table_info(users) []

13:12:46.309 [info]   Cache warmed up for 3 tables

13:12:46.309 [info] Successful: 1

13:12:46.309 [info] Failed: 0
