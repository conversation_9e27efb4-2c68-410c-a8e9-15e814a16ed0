defmodule Test.RelationCase do
  use ExUnit.CaseTemplate

  using do
    quote do
      use Test.DoctestCase

      alias Test.Repos.Sqlite
      alias Test.Repos.Postgres

      import Test.RelationCase
    end
  end

  setup tags do
    if tags[:test_type] != :doctest do
      adapter = Map.get(tags, :adapter, String.to_atom(System.get_env("ADAPTER", "sqlite")))

      # For async tests, checkout connection directly; for sync tests, use shared mode
      if tags[:async] do
        :ok = Test.Repos.checkout!(adapter)
      else
        :ok = Test.Repos.start_owner!(adapter, shared: true)
      end

      # Create a test-specific ID to ensure unique module names per test
      test_id = :erlang.phash2(self(), 1_000_000)

      # Store the test_id in the process dictionary so the relation/1 helper can access it
      Process.put(:test_id, test_id)

      # Set cache directory suffix for async test isolation
      if tags[:async] do
        Drops.Relation.Cache.set_cache_suffix("test_#{test_id}")
      end

      context =
        Enum.reduce(Map.get(tags, :relations, []), %{}, fn name, context ->
          relation_module = create_relation(name, adapter: adapter, test_id: test_id)
          # Store the mapping in the global registry
          Test.Relations.put(name, relation_module)
          Map.put(context, name, relation_module)
        end)

      on_exit(fn ->
        if tags[:async] do
          :ok = Test.Repos.checkin!(adapter)
          # Clear cache suffix for async tests
          Drops.Relation.Cache.set_cache_suffix(nil)
        else
          :ok = Test.Repos.stop_owner(adapter)
        end

        Test.cleanup_relation_modules(Map.values(context))
        Test.Relations.cleanup(test_id)
      end)

      {:ok, Map.merge(context, %{adapter: adapter, repo: repo(adapter)})}
    else
      :ok
    end
  end

  defmacro relation(name, opts) do
    quote do
      setup context do
        test_id = :erlang.phash2(self(), 1_000_000)
        Process.put(:test_id, test_id)
        opts_with_test_id = Keyword.put(unquote(Macro.escape(opts)), :test_id, test_id)
        relation_module = create_relation(unquote(name), opts_with_test_id)

        # Store the mapping in the global registry
        Test.Relations.put(unquote(name), relation_module)

        on_exit(fn ->
          Test.cleanup_relation_modules(relation_module)
          Test.Relations.cleanup(test_id)
        end)

        {:ok, Map.put(context, unquote(name), relation_module)}
      end
    end
  end

  @doc """
  Helper macro to resolve relation module names dynamically in test schemas.

  This allows tests to reference other relations without hardcoding module names:

      relation(:associations) do
        schema("associations") do
          has_many(:items, relation(:association_items))
          belongs_to(:parent, relation(:association_parents))
        end
      end
  """
  defmacro relation(name) when is_atom(name) do
    quote do
      test_id = Process.get(:test_id)

      if test_id do
        relation_name = Macro.camelize(Atom.to_string(unquote(name)))
        Module.concat([Test, Relations, "#{relation_name}#{test_id}"])
      else
        # Fallback to original naming if no test_id (for backwards compatibility)
        relation_name = Macro.camelize(Atom.to_string(unquote(name)))
        Module.concat([Test, Relations, relation_name])
      end
    end
  end

  defmacro adapters(adapter_list, do: block) do
    for adapter <- adapter_list do
      quote do
        describe "with #{unquote(adapter)} adapter" do
          setup do
            {:ok, adapter: unquote(adapter)}
          end

          unquote(block)
        end
      end
    end
  end

  def create_relation(name, opts) do
    adapter = Keyword.get(opts, :adapter, :sqlite)
    test_id = Keyword.get(opts, :test_id, :erlang.phash2(self(), 1_000_000))
    repo = repo(adapter)
    table_name = Atom.to_string(name)
    relation_name = Macro.camelize(table_name)

    # Make module name unique per test process to avoid conflicts in async tests
    module_name = Module.concat([Test, Relations, "#{relation_name}#{test_id}"])

    block =
      Keyword.get(
        opts,
        :do,
        quote do
          schema(unquote(table_name), infer: true)
        end
      )

    # Transform the block to replace Test.Relations.get/1 calls with actual module names
    transformed_block = transform_relation_references(block, test_id)

    # Clean up any existing module to avoid conflicts
    Test.cleanup_relation_modules(module_name)

    # Warm up cache for this specific table - this should work now that we have proper connection ownership
    {:ok, _} = Drops.Relation.Cache.warm_up(repo, [table_name])

    {:module, relation_module, _bytecode, _result} =
      Module.create(
        module_name,
        quote do
          use Drops.Relation, repo: unquote(repo)
          unquote(transformed_block)
        end,
        Macro.Env.location(__ENV__)
      )

    relation_module
  end

  def repo(:sqlite), do: Test.Repos.Sqlite
  def repo(:postgres), do: Test.Repos.Postgres

  # Transform Test.Relations.get/1 calls to actual module names
  defp transform_relation_references(ast, test_id) do
    Macro.prewalk(ast, fn
      # Match Test.Relations.get(:relation_name) calls
      {{:., _, [{:__aliases__, _, [:Test, :Relations]}, :get]}, _, [relation_name]}
      when is_atom(relation_name) ->
        relation_name_str = Macro.camelize(Atom.to_string(relation_name))
        Module.concat([Test, Relations, "#{relation_name_str}#{test_id}"])

      # Keep everything else as-is
      node ->
        node
    end)
  end

  @doc """
  Helper for asserting column properties in SQL Database tables.

  ## Examples

      assert_column(table, :id, :integer, primary_key: true)
      assert_column(table, :email, :string, nullable: true, default: nil)
  """
  def assert_column(table, column_name, expected_type, opts \\ []) do
    column = table[column_name]

    assert column != nil, "Column #{column_name} not found in table"

    assert column.type == expected_type,
           "Expected column #{column_name} to have type #{inspect(expected_type)}, got #{inspect(column.type)}"

    Enum.each(opts, fn {key, expected_value} ->
      actual_value = Map.get(column.meta, key)

      assert actual_value == expected_value,
             "Expected column #{column_name} to have #{key}: #{inspect(expected_value)}, got #{inspect(actual_value)}"
    end)
  end

  @doc """
  Helper for asserting field properties in Relation schemas.

  ## Examples

      assert_field(schema, :id, :id, primary_key: true, type: :integer)
      assert_field(schema, :email, :string, nullable: true)
  """
  def assert_field(schema, field_name, expected_type, opts \\ []) do
    field = Drops.Relation.Schema.find_field(schema, field_name)

    assert field != nil, "Field #{field_name} not found in schema"

    assert field.type == expected_type,
           "Expected field #{field_name} to have type #{inspect(expected_type)}, got #{inspect(field.type)}"

    Enum.each(opts, fn {key, expected_value} ->
      actual_value = Map.get(field.meta, key, Map.get(field, key))

      assert actual_value == expected_value,
             "Expected field #{field_name} to have #{key}: #{inspect(expected_value)}, got #{inspect(actual_value)}"
    end)
  end
end
