defmodule Drops.Relation.AssociationsTest do
  use Test.RelationCase, async: true

  describe "defining associations" do
    relation(:user_groups) do
      schema("user_groups") do
        belongs_to(:user, Test.Relations.get(:users))
        belongs_to(:group, Test.Relations.get(:groups))
      end
    end

    relation(:users) do
      schema("users") do
        has_many(:user_groups, Test.Relations.get(:user_groups))
        has_many(:groups, through: [:user_groups, :group])
      end
    end

    relation(:groups) do
      schema("groups") do
        has_many(:user_groups, Test.Relations.get(:user_groups))
        has_many(:users, through: [:user_groups, :user])
      end
    end

    test "returns relation view", %{users: users, groups: groups, user_groups: user_groups} do
      now = NaiveDateTime.utc_now() |> NaiveDateTime.truncate(:second)

      {:ok, user} = users.insert(%{name: "<PERSON>", inserted_at: now, updated_at: now})
      {:ok, group} = groups.insert(%{name: "<PERSON><PERSON>", inserted_at: now, updated_at: now})

      user_groups.insert(%{
        user_id: user.id,
        group_id: group.id,
        inserted_at: now,
        updated_at: now
      })

      user = users.preload(:groups) |> Enum.at(0)

      assert %{name: "Jade", groups: [%{name: "Admins"}]} = user
    end
  end
end
